import tkinter as tk
from tkinter import ttk, messagebox, Menu
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import gspread
from google.oauth2.service_account import Credentials
from datetime import datetime
import json
import os

# Kanal verilerini saklamak için JSON dosyası
CHANNELS_DATA_FILE = 'channels_data.json'
CHANNEL_VIDEOS_FILE = 'channel_videos.json'

def clean_saved_video_urls():
    try:
        # Kanal videolarını yükle
        if os.path.exists(CHANNEL_VIDEOS_FILE):
            with open(CHANNEL_VIDEOS_FILE, 'r', encoding='utf-8') as f:
                channels_data = json.load(f)

            # Her kanal için
            data_changed = False
            for channel_name, videos in channels_data.items():
                # Her video için
                for video in videos:
                    # URL'yi temizle
                    original_url = video['url']
                    cleaned_url = original_url.split('&')[0] if '&' in original_url else original_url

                    # Eğer URL değiştiyse
                    if cleaned_url != original_url:
                        video['url'] = cleaned_url
                        data_changed = True

            # Eğer değişiklik yapıldıysa kaydet
            if data_changed:
                with open(CHANNEL_VIDEOS_FILE, 'w', encoding='utf-8') as f:
                    json.dump(channels_data, f, ensure_ascii=False, indent=4)
                print("Kayıtlı video URL'leri temizlendi.")

    except Exception as e:
        print(f"URL temizleme hatası: {e}")

def load_channel_videos_data():
    try:
        if os.path.exists(CHANNEL_VIDEOS_FILE):
            with open(CHANNEL_VIDEOS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    except Exception as e:
        print(f"Kanal videoları yükleme hatası: {e}")
        return {}

def save_channel_videos_data(channel_name, videos_data):
    try:
        # Mevcut verileri yükle
        all_data = load_channel_videos_data()

        # Kanal verilerini güncelle
        all_data[channel_name] = videos_data

        # JSON dosyasına kaydet
        with open(CHANNEL_VIDEOS_FILE, 'w', encoding='utf-8') as f:
            json.dump(all_data, f, ensure_ascii=False, indent=4)

        print(f"{channel_name} kanalı için {len(videos_data)} video kaydedildi.")
    except Exception as e:
        print(f"Kanal videoları kaydetme hatası: {e}")

# Ana pencere oluşturma ve özelleştirme
root = tk.Tk()
root.title("YouTube Video Sıralama Analizi")
root.geometry("1000x800")
root.configure(bg='white')

# Başlangıçta URL'leri temizle
clean_saved_video_urls()

# Checkbox için metin sembolleri tanımla
CHECK_ON = "☑"
CHECK_OFF = "☐"

# Google Sheets bağlantısı için try-except bloğu ekleyelim
try:
    # Google Sheets API için kimlik bilgilerini yükleyin
    scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/spreadsheets"]
    creds = Credentials.from_service_account_file('credentials.json', scopes=scope)
    client = gspread.authorize(creds)

    # Sütun tanımlamaları
    ranks_columns = ['URL', 'Keyword', 'Tag', 'Channel', 'Current Rank', 'Previous Rank', 'Current Views',
                    'Previous Views', 'Views Difference', 'Upload Time', 'Last Update', 'Rank Change']

    column_widths = {
        'URL': 300,
        'Keyword': 150,
        'Tag': 100,
        'Channel': 150,
        'Current Rank': 100,
        'Previous Rank': 100,
        'Current Views': 100,
        'Previous Views': 100,
        'Views Difference': 100,
        'Upload Time': 150,
        'Last Update': 150,
        'Rank Change': 80
    }

    # Google Sheet dosyasına bağlanın
    spreadsheet = client.open("youtube rank checker")
    sheet = spreadsheet.sheet1  # İlk çalışma sayfasını seçin

    # Ranks sayfasını kontrol et ve oluştur
    try:
        ranks_sheet = spreadsheet.worksheet("Ranks")
    except gspread.WorksheetNotFound:
        ranks_sheet = spreadsheet.add_worksheet(title="Ranks", rows="1000", cols="20")

    # Ranks sayfası sütun başlıklarını kontrol et ve güncelle
    ranks_headers = ranks_sheet.row_values(1)
    ranks_required_headers = ['URL', 'Keyword', 'Tag', 'Channel', 'Current Rank', 'Previous Rank', 'Current Views', 'Previous Views',
                            'Views Difference', 'Upload Time', 'Last Update', 'Rank Change']
    if not ranks_headers:
        ranks_sheet.append_row(ranks_required_headers)
    elif ranks_headers != ranks_required_headers:
        ranks_sheet.clear()
        ranks_sheet.append_row(ranks_required_headers)

    # Ana sayfa sütun başlıklarını kontrol et ve güncelle
    headers = sheet.row_values(1)
    required_headers = ['Rank', 'Keyword', 'URL', 'Views', 'Upload Time', 'New Rank', 'New Views', 'Last Update', 'Views Difference']
    if not headers:
        sheet.append_row(required_headers)
    elif headers != required_headers:
        sheet.clear()
        sheet.append_row(required_headers)
except Exception as e:
    print(f"Google Sheets bağlantı hatası: {e}")
    # Hata durumunda boş değerler ata
    spreadsheet = None
    sheet = None
    ranks_sheet = None

# Ranks verilerini saklamak için JSON dosyası
RANKS_DATA_FILE = 'ranks_data.json'

# Ranks verilerini yükle
def load_ranks_data():
    try:
        if os.path.exists(RANKS_DATA_FILE):
            with open(RANKS_DATA_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    except Exception as e:
        print(f"Ranks veri yükleme hatası: {e}")
        return {}

# Ranks verilerini kaydet
def save_ranks_data(data):
    try:
        with open(RANKS_DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
    except Exception as e:
        print(f"Ranks veri kaydetme hatası: {e}")

def update_ranks_cell_color(row_number, old_rank, new_rank):
    worksheet_id = ranks_sheet._properties['sheetId']

    # Renk tanımlamaları
    green_color = {"red": 0.8, "green": 1, "blue": 0.8}    # Açık yeşil
    red_color = {"red": 1, "green": 0.8, "blue": 0.8}      # Açık kırmızı
    gray_color = {"red": 0.9, "green": 0.9, "blue": 0.9}   # Açık gri

    try:
        old_rank = int(old_rank) if old_rank else 999
        new_rank = int(new_rank) if new_rank else 999

        # Renk seçimi
        if new_rank < old_rank:  # İyileşme
            background_color = green_color
            change_text = "↑"
        elif new_rank > old_rank:  # Kötüleşme
            background_color = red_color
            change_text = "↓"
        else:  # Değişim yok
            background_color = gray_color
            change_text = "="

        # Rank Change sütununu güncelle
        ranks_sheet.update_cell(row_number, 11, change_text)

        # Hücre formatını güncelle
        format_request = {
            "requests": [
                {
                    "updateCells": {
                        "rows": [
                            {
                                "values": [
                                    {
                                        "userEnteredFormat": {
                                            "backgroundColor": background_color
                                        }
                                    }
                                ]
                            }
                        ],
                        "fields": "userEnteredFormat.backgroundColor",
                        "range": {
                            "sheetId": worksheet_id,
                            "startRowIndex": row_number - 1,
                            "endRowIndex": row_number,
                            "startColumnIndex": 3,  # Current Rank sütunu
                            "endColumnIndex": 4
                        }
                    }
                }
            ]
        }

        spreadsheet.batch_update(format_request)
    except Exception as e:
        print(f"Renk güncelleme hatası: {e}")

def update_ranks_record(url, keyword, video_position, video_views, video_upload_time):
    try:
        # Mevcut verileri yükle
        ranks_data = load_ranks_data()
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        if url in ranks_data:
            # Mevcut kaydı güncelle
            current_data = ranks_data[url]

            # Önceki değerleri sakla
            current_data['Previous Rank'] = current_data.get('Current Rank', '')
            current_data['Previous Views'] = current_data.get('Current Views', '')

            # Yeni değerleri güncelle
            current_data['Current Rank'] = str(video_position)
            current_data['Current Views'] = str(video_views)
            current_data['Last Update'] = current_time

            # Views farkını hesapla
            try:
                old_views = int(str(current_data['Previous Views']).replace(',', '')) if current_data['Previous Views'] else 0
                new_views = int(str(video_views).replace(',', '')) if video_views != "Bilinmiyor" else 0
                current_data['Views Difference'] = str(new_views - old_views)
            except:
                current_data['Views Difference'] = "0"

            # Rank değişimini hesapla
            try:
                old_rank = int(current_data['Previous Rank']) if current_data['Previous Rank'] else 999
                new_rank = int(video_position) if str(video_position).isdigit() else 999
                if new_rank < old_rank:
                    current_data['Rank Change'] = "↑"
                elif new_rank > old_rank:
                    current_data['Rank Change'] = "↓"
                else:
                    current_data['Rank Change'] = "="
            except:
                current_data['Rank Change'] = "="

        else:
            # Yeni kayıt oluştur
            ranks_data[url] = {
                'URL': url,
                'Keyword': keyword,
                'Tag': '',
                'Channel': '',
                'Current Rank': str(video_position),
                'Previous Rank': '',
                'Current Views': str(video_views),
                'Previous Views': '',
                'Views Difference': '0',
                'Upload Time': video_upload_time,
                'Last Update': current_time,
                'Rank Change': '='
            }

        # Verileri kaydet
        save_ranks_data(ranks_data)

        # Tabloyu güncelle
        update_ranks_table()

    except Exception as e:
        print(f"Ranks kayıt güncelleme hatası: {e}")

def update_cell_color(row_number, old_rank, new_rank):
    worksheet_id = sheet._properties['sheetId']

    # Renk tanımlamaları
    green_color = {"red": 0.8, "green": 1, "blue": 0.8}    # Açık yeşil
    red_color = {"red": 1, "green": 0.8, "blue": 0.8}      # Açık kırmızı
    gray_color = {"red": 0.9, "green": 0.9, "blue": 0.9}   # Açık gri

    # Renk seçimi
    if int(new_rank) < int(old_rank):  # İyileşme
        background_color = green_color
    elif int(new_rank) > int(old_rank):  # Kötüleşme
        background_color = red_color
    else:  # Değişim yok
        background_color = gray_color

    # Hücre formatını güncelle
    format_request = {
        "requests": [
            {
                "updateCells": {
                    "rows": [
                        {
                            "values": [
                                {
                                    "userEnteredFormat": {
                                        "backgroundColor": background_color
                                    }
                                }
                            ]
                        }
                    ],
                    "fields": "userEnteredFormat.backgroundColor",
                    "range": {
                        "sheetId": worksheet_id,
                        "startRowIndex": row_number - 1,
                        "endRowIndex": row_number,
                        "startColumnIndex": 5,
                        "endColumnIndex": 6
                    }
                }
            }
        ]
    }

    try:
        spreadsheet.batch_update(format_request)
    except Exception as e:
        print(f"Renk güncelleme hatası: {e}")

# Geçmiş sorguları kaydetmek için liste
search_history = []
# Durdurma kontrolü için global değişken
stop_search = False

# Sıralama için gerekli değişkenler
sort_column = None
sort_reverse = False

def treeview_sort_column(tv, col, reverse):
    try:
        # Mevcut verileri al
        l = [(tv.set(k, col), k) for k in tv.get_children('')]

        # Sayısal sütunlar için özel sıralama
        if col in ['Current Rank', 'Previous Rank', 'Current Views', 'Previous Views', 'Views Difference']:
            if col == 'Current Rank':
                # Current Rank için özel sıralama (0 en büyük değer olarak)
                l = [(float('inf') if val.strip() == '0' else float(val.replace(',', '')) if val.strip() and val.strip() != 'Bulunamadı' else float('inf'), k) for val, k in l]
            else:
                l = [(float(val.replace(',', '')) if val.strip() and val.strip() != 'Bulunamadı' else float('inf'), k) for val, k in l]

        # Tarih sütunları için özel sıralama
        elif col in ['Upload Time', 'Last Update']:
            l = [(val if val.strip() and val.strip() != 'Bulunamadı' else '9999-99-99', k) for val, k in l]

        # Sıralama yap
        l.sort(reverse=reverse)

        # Sıralanmış öğeleri yeniden yerleştir
        for index, (val, k) in enumerate(l):
            tv.move(k, '', index)

        # Bir sonraki tıklama için sıralama yönünü değiştir
        tv.heading(col, command=lambda: treeview_sort_column(tv, col, not reverse))

    except Exception as e:
        print(f"Sıralama hatası: {e}")

# Treeview stil tanımlamaları
style = ttk.Style()
style.configure("Treeview", rowheight=25)
style.configure("Treeview.Heading", font=('Calibri', 10, 'bold'))
style.map("Treeview",
          foreground=[('selected', '#000000')],
          background=[('selected', '#f0f0f0')])

def create_custom_style():
    style = ttk.Style()
    style.theme_use('clam')  # Modern tema kullanımı

    # Ana renk paleti
    primary_color = "#2196F3"  # Material Design Blue
    secondary_color = "#757575"  # Material Design Gray
    bg_color = "#FFFFFF"  # White background

    # Entry style
    style.configure('Custom.TEntry',
                   padding=10,
                   fieldbackground=bg_color,
                   borderwidth=1)

    # Button style
    style.configure('Custom.TButton',
                   padding=(20, 10),
                   background=primary_color,
                   foreground='white',
                   font=('Segoe UI', 10, 'bold'))

    # Label style
    style.configure('Custom.TLabel',
                   padding=5,
                   font=('Segoe UI', 10),
                   foreground=secondary_color)

    # Frame style
    style.configure('Custom.TFrame',
                   background=bg_color)

    # Channel button styles
    style.configure('Channel.TFrame',
                   relief='raised',
                   borderwidth=1)

    style.configure('Channel.TLabel',
                   font=('Segoe UI', 10, 'bold'),
                   foreground='#333333',
                   padding=3)

    style.configure('ChannelSmall.TLabel',
                   font=('Segoe UI', 8),
                   foreground='#666666',
                   padding=2)

def stop_search_process():
    global stop_search
    stop_search = True
    stop_button.configure(state='disabled')
    search_button.configure(state='normal', text="Ara")

def process_bulk_search():
    global stop_search
    stop_search = False
    stop_button.configure(state='normal')

    # Toplu sorgu metnini al
    bulk_queries = bulk_text.get("1.0", tk.END).strip()

    if not bulk_queries:
        messagebox.showerror("Hata", "Lütfen en az bir sorgu girin!")
        return

    # Sorguları satır satır ayır
    queries = []
    for line in bulk_queries.split('\n'):
        if '|' in line:
            url, keyword = line.split('|', 1)
            queries.append((url.strip(), keyword.strip()))

    if not queries:
        messagebox.showerror("Hata", "Geçerli sorgu bulunamadı!\nFormat: URL | Anahtar Kelime")
        return

    # Ana pencereyi göster
    notebook.select(0)  # Ana sekmeye geç

    # Her sorgu için arama yap
    for url, keyword in queries:
        if stop_search:
            messagebox.showinfo("Bilgi", "Sorgu işlemi durduruldu!")
            stop_button.configure(state='disabled')
            break

        # URL ve keyword'ü giriş alanlarına yerleştir
        url_entry.delete(0, tk.END)
        url_entry.insert(0, url)
        keyword_entry.delete(0, tk.END)
        keyword_entry.insert(0, keyword)

        # Aramayı başlat
        search_video()

        # 5 saniye bekle
        time.sleep(5)

    stop_button.configure(state='disabled')

def search_video():
    try:
        global stop_search
        driver = None

        # Arama başladığında butonu devre dışı bırak ve metni değiştir
        search_button.configure(state='disabled', text="Aranıyor...")
        root.update()

        if stop_search:
            search_button.configure(state='normal', text="Ara")
            return

        # Kullanıcının girdiği URL ve Keyword'ü al
        keyword = keyword_entry.get()
        url = url_entry.get()

        if not keyword or not url:
            messagebox.showerror("Hata", "Lütfen URL ve anahtar kelime girin!")
            search_button.configure(state='normal', text="Ara")
            return

        # Video ID'sini URL'den çıkaralım
        target_video_id = url.split("watch?v=")[-1]

        if stop_search:
            search_button.configure(state='normal', text="Ara")
            return

        # Chrome ayarlarını yapalım
        options = webdriver.ChromeOptions()
        options.add_argument('--headless=new')  # Yeni headless modu
        options.add_argument('--disable-gpu')
        options.add_argument('--incognito')  # Gizli mod
        options.add_argument('--start-maximized')
        options.add_argument('--disable-software-rasterizer')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option('excludeSwitches', ['enable-logging'])
        options.add_experimental_option('excludeSwitches', ['enable-automation'])

        # Çerez ve önbellek temizleme
        options.add_argument('--disable-application-cache')
        options.add_argument('--disable-cache')
        options.add_argument('--disable-offline-load-stale-cache')
        options.add_argument('--disk-cache-size=0')
        options.add_argument('--no-cache')
        options.add_argument('--disable-cookies')
        options.add_argument('--disable-storage')
        options.add_argument('--disable-databases')

        # Türkiye lokasyonu için dil ve bölge ayarları
        options.add_argument('--lang=tr')
        options.add_argument('--accept-lang=tr-TR,tr')

        # Kullanıcı ajanı (Türkiye'den normal bir kullanıcı gibi)
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36')

        try:
            # WebDriver başlatma
            service = Service()
            driver = webdriver.Chrome(service=service, options=options)

            # Çerezleri temizle
            driver.delete_all_cookies()

        except Exception as e:
            # WebDriver başlatılamasa bile kayıt alalım
            video_position = 0
            video_views = "0"
            video_upload_time = "Bulunamadı"

            results_text.insert(tk.END,
                f"Video sırası: {video_position}\n"
                f"Aranan kelime: {keyword}\n"
                f"Video URL: {url}\n"
                f"İzlenme sayısı: {video_views}\n"
                f"Yüklenme zamanı: {video_upload_time}\n"
                f"Hata: Chrome WebDriver başlatılamadı - {e}\n"
            )

            # Ranks tablosuna kaydet
            update_ranks_record(url, keyword, video_position, video_views, video_upload_time)

            search_button.configure(state='normal', text="Ara")
            return

        # Progress bar'ı göster ve güncelle
        progress_var.set(10)
        root.update()

        if stop_search:
            if driver:
                driver.quit()
            search_button.configure(state='normal', text="Ara")
            return

        # YouTube'a git ve dil/bölge ayarını kontrol et
        driver.get('https://www.youtube.com/?gl=TR&hl=tr')
        time.sleep(2)
        progress_var.set(20)
        root.update()

        if stop_search:
            driver.quit()
            search_button.configure(state='normal', text="Ara")
            return

        # Arama kutusunu bul
        search_box = driver.find_element(By.NAME, 'search_query')
        time.sleep(1)
        progress_var.set(30)
        root.update()

        if stop_search:
            driver.quit()
            search_button.configure(state='normal', text="Ara")
            return

        # Arama yapmak istediğimiz kelimeyi gir
        search_box.send_keys(keyword)
        time.sleep(1)
        progress_var.set(40)
        root.update()

        if stop_search:
            driver.quit()
            search_button.configure(state='normal', text="Ara")
            return

        # Arama butonuna bas
        search_box.send_keys(Keys.RETURN)
        time.sleep(2)
        progress_var.set(50)
        root.update()

        if stop_search:
            driver.quit()
            search_button.configure(state='normal', text="Ara")
            return

        # Video başlıklarını kontrol etme
        video_titles = driver.find_elements(By.XPATH, '//a[@id="video-title"]')
        print(f"Toplam {len(video_titles)} video başlığı bulundu.")

        results_text.delete(1.0, tk.END)

        video_urls = set()
        found_video = False
        result_index = 1
        video_position = 0  # Varsayılan değer 0 olarak ayarlandı
        video_views = "0"
        video_upload_time = "Bulunamadı"

        progress_var.set(60)
        root.update()

        while len(video_urls) < 100 and not stop_search:
            for index, video_title in enumerate(video_titles):
                if stop_search:
                    break

                video_href = video_title.get_attribute('href')

                if video_href is not None and 'watch?v=' in video_href:
                    if video_href not in video_urls:
                        video_urls.add(video_href)

                        if target_video_id in video_href:
                            # Sadece hedef video bulunduğunda meta verileri çek
                            views = "0"
                            upload_time = "Bulunamadı"

                            try:
                                # Video container'ı bul
                                video_container = video_title.find_element(By.XPATH, './/ancestor::ytd-video-renderer')

                                # Meta verileri bul
                                meta_items = video_container.find_elements(By.CSS_SELECTOR, 'span.inline-metadata-item')

                                if meta_items:
                                    # İzlenme sayısı (ilk span)
                                    if len(meta_items) > 0:
                                        views_text = meta_items[0].text
                                        if "görüntüleme" in views_text.lower():
                                            views = views_text.split(" görüntüleme")[0].strip()

                                    # Yüklenme zamanı (ikinci span)
                                    if len(meta_items) > 1:
                                        upload_time = meta_items[1].text

                            except Exception as e:
                                print(f"Meta veri okuma hatası: {str(e)}")

                            video_position = result_index
                            video_views = views
                            video_upload_time = upload_time
                            found_video = True
                            break
                        result_index += 1

                if len(video_urls) >= 100 or found_video:
                    break

            if len(video_urls) >= 100 or found_video:
                break

            # Scroll yap ve yeni videoları yükle
            last_height = driver.execute_script("return document.documentElement.scrollHeight")
            driver.execute_script("window.scrollTo(0, document.documentElement.scrollHeight);")
            time.sleep(2)

            # Yeni yüksekliği kontrol et
            new_height = driver.execute_script("return document.documentElement.scrollHeight")
            if new_height == last_height:  # Sayfa sonuna gelindi
                break

            # Yeni video başlıklarını al
            video_titles = driver.find_elements(By.XPATH, '//a[@id="video-title"]')

        progress_var.set(90)
        root.update()

        # Her durumda sonuçları göster ve kaydet
        results_text.insert(tk.END,
            f"Video sırası: {video_position}\n"
            f"Aranan kelime: {keyword}\n"
            f"Video URL: {url}\n"
            f"İzlenme sayısı: {video_views}\n"
            f"Yüklenme zamanı: {video_upload_time}\n"
        )

        search_history.append([
            video_position,
            keyword,
            url,
            video_views,
            video_upload_time
        ])
        update_history_table()

        # Her durumda Ranks tablosuna kaydet
        update_ranks_record(url, keyword, video_position, video_views, video_upload_time)

        if driver:
            driver.quit()

        progress_var.set(100)
        search_button.configure(state='normal', text="Ara")
        root.update()

    except Exception as e:
        # Hata durumunda bile kayıt alalım
        video_position = 0
        video_views = "0"
        video_upload_time = "Bulunamadı"

        messagebox.showerror("Hata", f"Bir hata oluştu: {e}")
        results_text.insert(tk.END,
            f"Video sırası: {video_position}\n"
            f"Aranan kelime: {keyword}\n"
            f"Video URL: {url}\n"
            f"İzlenme sayısı: {video_views}\n"
            f"Yüklenme zamanı: {video_upload_time}\n"
            f"Hata: {e}\n"
        )

        # Hata durumunda da Ranks tablosuna kaydet
        update_ranks_record(url, keyword, video_position, video_views, video_upload_time)

        search_button.configure(state='normal', text="Ara")
        progress_var.set(0)
        if driver:
            driver.quit()

def update_history_table():
    history_tree.delete(*history_tree.get_children())
    for record in search_history:
        history_tree.insert('', 'end', values=record)

# Ranks tablosu güncelleme fonksiyonu
def update_ranks_table():
    try:
        # Mevcut tabloyu temizle
        ranks_tree.delete(*ranks_tree.get_children())

        # Verileri yükle
        ranks_data = load_ranks_data()
        all_data = []

        # Dictionary'den listeye çevir
        for url, data in ranks_data.items():
            row_data = [
                "❌",  # Silme butonu
                data.get('URL', ''),
                data.get('Keyword', ''),
                data.get('Tag', ''),
                data.get('Channel', ''),
                data.get('Current Rank', ''),
                data.get('Previous Rank', ''),
                data.get('Current Views', ''),
                data.get('Previous Views', ''),
                data.get('Views Difference', ''),
                data.get('Upload Time', ''),
                data.get('Last Update', ''),
                data.get('Rank Change', '')
            ]
            all_data.append(row_data)

        # Sıralama kriterine göre sırala
        sort_key = sort_var.get()
        if sort_key == "rank":
            all_data.sort(key=lambda x: int(x[5]) if str(x[5]).isdigit() else 999)  # Current Rank sütunu
        elif sort_key == "views":
            all_data.sort(key=lambda x: int(x[9]) if str(x[9]).isdigit() else 0, reverse=True)  # Views Difference sütunu
        elif sort_key == "update":
            all_data.sort(key=lambda x: x[11] if x[11] else '', reverse=True)  # Last Update sütunu

        # Tag ve Channel filtresini uygula
        filter_text = tag_filter.get().lower()
        if filter_text:
            all_data = [row for row in all_data if
                       filter_text in str(row[3]).lower() or  # Tag sütunu
                       filter_text in str(row[4]).lower()]    # Channel sütunu

        # Renk tag'lerini oluştur
        ranks_tree.tag_configure('up_arrow', foreground='#00ff00')  # Yeşil
        ranks_tree.tag_configure('down_arrow', foreground='#ff0000')  # Kırmızı
        ranks_tree.tag_configure('equal_sign', foreground='black')  # Siyah
        ranks_tree.tag_configure('delete_button', foreground='red')  # Silme butonu kırmızı

        # Verileri tabloya ekle
        for row in all_data:
            item = ranks_tree.insert('', 'end', values=row)

            # Silme butonu için kırmızı renk
            if row[0] == "❌":
                ranks_tree.item(item, tags=('delete_button',))

            # Rank Change sütunu için renk
            rank_change = row[12]  # Rank Change sütunu
            if rank_change == "↑":
                ranks_tree.item(item, tags=('up_arrow',))
            elif rank_change == "↓":
                ranks_tree.item(item, tags=('down_arrow',))
            elif rank_change == "=":
                ranks_tree.item(item, tags=('equal_sign',))

    except Exception as e:
        print(f"Ranks tablosu güncelleme hatası: {e}")

# Notebook (Sekmeli arayüz) oluştur
notebook = ttk.Notebook(root)
notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

# Ana sekme frame'i
main_frame = ttk.Frame(notebook, style='Custom.TFrame')
notebook.add(main_frame, text='Ana Sayfa')

# Sıralama değişkeni
sort_var = tk.StringVar(value="rank")

# Toplu Sorgu sekmesi
bulk_frame = ttk.Frame(notebook, style='Custom.TFrame')
notebook.add(bulk_frame, text='Toplu Sorgu')

# Ranks sekmesi
ranks_frame = ttk.Frame(notebook, style='Custom.TFrame')
notebook.add(ranks_frame, text='Ranks')

# Kanallar sekmesi
channels_frame = ttk.Frame(notebook, style='Custom.TFrame')
notebook.add(channels_frame, text='Kanallar')

# Güncel sekmesi
guncel_frame = ttk.Frame(notebook, style='Custom.TFrame')
notebook.add(guncel_frame, text='Güncel')

# Kanallar sekmesi için üst frame (kanal URL girişi ve butonlar)
channels_top_frame = ttk.Frame(channels_frame, style='Custom.TFrame')
channels_top_frame.pack(fill=tk.X, padx=10, pady=5)

# Kanal URL girişi
channel_url_entry = ttk.Entry(channels_top_frame, style='Custom.TEntry', width=50)
channel_url_entry.pack(side=tk.LEFT, padx=5)
channel_url_entry.insert(0, "Kanal URL'sini girin")

# Kanal ekle butonu
add_channel_button = ttk.Button(
    channels_top_frame,
    text="Kanal Ekle",
    style='Custom.TButton',
    command=lambda: add_channel()
)
add_channel_button.pack(side=tk.LEFT, padx=5)

# Check butonu
check_button = ttk.Button(
    channels_top_frame,
    text="Check",
    style='Custom.TButton',
    width=10,
    command=lambda: check_video_ranks()
)
check_button.pack(side=tk.LEFT, padx=5)

# Kanal listesi için frame (üstte)
channels_list_frame = ttk.Frame(channels_frame, style='Custom.TFrame')
channels_list_frame.pack(fill=tk.X, padx=10, pady=5)

# Kanal butonları için canvas ve scrollbar
channels_canvas = tk.Canvas(channels_list_frame, height=60, background='#f5f5f5')
channels_canvas.pack(fill=tk.X, expand=True)

channels_scrollbar = ttk.Scrollbar(channels_list_frame, orient=tk.HORIZONTAL, command=channels_canvas.xview)
channels_scrollbar.pack(fill=tk.X)

channels_buttons_frame = ttk.Frame(channels_canvas, style='Custom.TFrame')
channels_canvas.create_window((0, 0), window=channels_buttons_frame, anchor='nw')

# Video listesi için frame (altta)
videos_frame = ttk.Frame(channels_frame, style='Custom.TFrame')
videos_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

# Video listesi için Treeview
videos_tree = ttk.Treeview(videos_frame, columns=(
    'Select', 'Thumbnail', 'URL', 'Keyword', 'Başlık', 'Rank', 'Like', 'Yorum', 'İzlenme', 'Yüklenme Tarihi', 'Günlük Değişim'
), show='headings')

# Sütun başlıkları ve genişlikleri
videos_tree.heading('Select', text='')
videos_tree.heading('Thumbnail', text='')
videos_tree.heading('URL', text='Video URL')
videos_tree.heading('Keyword', text='Keyword')
videos_tree.heading('Başlık', text='Başlık')
videos_tree.heading('Rank', text='Rank')
videos_tree.heading('Like', text='Like')
videos_tree.heading('Yorum', text='Yorum')
videos_tree.heading('İzlenme', text='İzlenme')
videos_tree.heading('Yüklenme Tarihi', text='Yüklenme Tarihi')
videos_tree.heading('Günlük Değişim', text='Günlük Değişim')

videos_tree.column('Select', width=30, anchor='center')
videos_tree.column('Thumbnail', width=100, anchor='center')
videos_tree.column('URL', width=200)
videos_tree.column('Keyword', width=150)
videos_tree.column('Başlık', width=300)
videos_tree.column('Rank', width=80, anchor='center')
videos_tree.column('Like', width=100, anchor='center')
videos_tree.column('Yorum', width=100, anchor='center')
videos_tree.column('İzlenme', width=100, anchor='center')
videos_tree.column('Yüklenme Tarihi', width=150, anchor='center')
videos_tree.column('Günlük Değişim', width=100, anchor='center')

# Seçim kutucuğu için tag oluştur
videos_tree.tag_configure('checked')
videos_tree.tag_configure('unchecked')

# Scrollbar'lar
videos_vsb = ttk.Scrollbar(videos_frame, orient="vertical", command=videos_tree.yview)
videos_hsb = ttk.Scrollbar(videos_frame, orient="horizontal", command=videos_tree.xview)
videos_tree.configure(yscrollcommand=videos_vsb.set, xscrollcommand=videos_hsb.set)

# Treeview ve scrollbar'ları yerleştir
videos_vsb.pack(side='right', fill='y')
videos_hsb.pack(side='bottom', fill='x')
videos_tree.pack(fill='both', expand=True)

# Video silme butonu ve fonksiyonu
def delete_selected_videos():
    # Seçili videoları bul
    selected_items = [item for item in videos_tree.get_children()
                     if 'checked' in videos_tree.item(item, 'tags')]

    if not selected_items:
        messagebox.showinfo("Bilgi", "Lütfen silmek istediğiniz videoları seçin!")
        return

    # Onay sor
    if not messagebox.askyesno("Silme Onayı", f"{len(selected_items)} videoyu silmek istediğinizden emin misiniz?"):
        return

    try:
        # Mevcut kanal adını bul
        current_channel = None
        # Aktif sekmeyi kontrol et
        selected_tab = notebook.select()
        tab_text = notebook.tab(selected_tab, "text")

        if tab_text == "Kanallar":
            # İlk seçili videonun URL'sinden kanal adını bulmaya çalış
            for item in selected_items:
                url = videos_tree.item(item)['values'][2]  # URL 3. sütunda (index 2)
                if url and 'youtube.com' in url:
                    # URL'den video ID'sini çıkar
                    video_id = url.split('watch?v=')[-1].split('&')[0]

                    # Tüm kanal verilerini kontrol et
                    all_videos_data = load_channel_videos_data()
                    for ch_name, videos in all_videos_data.items():
                        for video in videos:
                            if video_id in video['url']:
                                current_channel = ch_name
                                break
                        if current_channel:
                            break
                    if current_channel:
                        break

        if not current_channel:
            messagebox.showerror("Hata", "Kanal bilgisi bulunamadı!")
            return

        # JSON verilerini yükle
        all_videos_data = load_channel_videos_data()
        if current_channel not in all_videos_data:
            messagebox.showerror("Hata", "Kanal verileri bulunamadı!")
            return

        channel_videos = all_videos_data[current_channel]

        # Silinecek URL'leri topla
        urls_to_delete = [videos_tree.item(item)['values'][2] for item in selected_items]

        # Videoları JSON'dan sil
        new_videos = [video for video in channel_videos if video['url'] not in urls_to_delete]
        all_videos_data[current_channel] = new_videos

        # JSON'a kaydet
        with open(CHANNEL_VIDEOS_FILE, 'w', encoding='utf-8') as f:
            json.dump(all_videos_data, f, ensure_ascii=False, indent=4)

        # Treeview'dan sil
        for item in selected_items:
            videos_tree.delete(item)

        messagebox.showinfo("Bilgi", f"{len(selected_items)} video başarıyla silindi!")

    except Exception as e:
        messagebox.showerror("Hata", f"Video silme işlemi sırasında bir hata oluştu: {e}")

# Silme butonu frame'i
delete_frame = ttk.Frame(videos_frame, style='Custom.TFrame')
delete_frame.pack(fill=tk.X, pady=5)

# Silme butonu
delete_button = ttk.Button(
    delete_frame,
    text="Seçili Videoları Sil",
    style='Custom.TButton',
    command=delete_selected_videos
)
delete_button.pack(side=tk.RIGHT, padx=5)

def load_channels_data():
    try:
        if os.path.exists(CHANNELS_DATA_FILE):
            with open(CHANNELS_DATA_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    except Exception as e:
        print(f"Kanal verisi yükleme hatası: {e}")
        return {}

def save_channels_data(data):
    try:
        with open(CHANNELS_DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
    except Exception as e:
        print(f"Kanal verisi kaydetme hatası: {e}")

def create_channel_button(url, channel_name, mail=""):
    # Frame oluştur (dikey düzen için)
    button_frame = ttk.Frame(channels_buttons_frame, style='Channel.TFrame')
    button_frame.pack(side=tk.LEFT, padx=4, pady=4)

    # Kanal adı etiketi
    channel_label = ttk.Label(button_frame, text=channel_name, style='Channel.TLabel')
    channel_label.pack(pady=(5,2))

    # Mail etiketi
    mail_label = ttk.Label(button_frame, text=mail, style='ChannelSmall.TLabel')
    mail_label.pack(pady=(0,5))

    # Frame'e tıklama olaylarını bağla
    button_frame.bind('<Button-1>', lambda e: load_channel_videos(url))
    channel_label.bind('<Button-1>', lambda e: load_channel_videos(url))
    mail_label.bind('<Button-1>', lambda e: load_channel_videos(url))
    channel_label.bind('<Double-Button-1>', lambda e: edit_channel_mail(url))
    mail_label.bind('<Double-Button-1>', lambda e: edit_channel_mail(url))

def edit_channel_mail(url):
    # Düzenleme penceresi oluştur
    edit_window = tk.Toplevel()
    edit_window.title("Mail Düzenle")
    edit_window.geometry("300x150")

    # Pencereyi merkeze al
    edit_window.transient(root)
    edit_window.grab_set()

    # Mail giriş alanı
    mail_label = ttk.Label(edit_window, text="Mail Adresi:", style='Custom.TLabel')
    mail_label.pack(pady=10)

    mail_entry = ttk.Entry(edit_window, style='Custom.TEntry')
    mail_entry.pack(pady=5)

    # Mevcut mail varsa göster
    channels_data = load_channels_data()
    if url in channels_data and 'mail' in channels_data[url]:
        mail_entry.insert(0, channels_data[url]['mail'])

    def save_mail():
        mail = mail_entry.get().strip()
        if mail:
            # Kanal verilerini güncelle
            channels_data = load_channels_data()
            if url in channels_data:
                channels_data[url]['mail'] = mail
                save_channels_data(channels_data)

                # Butonları yeniden yükle
                refresh_channel_buttons()

            edit_window.destroy()

    # Kaydet butonu
    save_button = ttk.Button(edit_window, text="Kaydet", style='Custom.TButton', command=save_mail)
    save_button.pack(pady=20)

def refresh_channel_buttons():
    # Mevcut butonları temizle
    for widget in channels_buttons_frame.winfo_children():
        widget.destroy()

    # Butonları yeniden oluştur
    channels_data = load_channels_data()
    for url, data in channels_data.items():
        channel_name = data.get('name', '')
        mail = data.get('mail', '')
        if channel_name:
            create_channel_button(url, channel_name, mail)

    # Canvas'ı güncelle
    channels_buttons_frame.update_idletasks()
    channels_canvas.configure(scrollregion=channels_canvas.bbox('all'))

def add_channel():
    url = channel_url_entry.get().strip()
    if not url:
        return

    try:
        # Kanal adını URL'den al
        channel_name = url.split('@')[1].split('/')[0] if '@' in url else url.split('/')[-1]

        # Kanal verilerini kaydet
        channels_data = load_channels_data()
        channels_data[url] = {
            'name': channel_name,
            'mail': '',  # Boş mail alanı ekle
            'added_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        save_channels_data(channels_data)

        # Kanal butonunu oluştur
        create_channel_button(url, channel_name)

        # Canvas'ı güncelle
        channels_buttons_frame.update_idletasks()
        channels_canvas.configure(scrollregion=channels_canvas.bbox('all'))

        # URL girişini temizle
        channel_url_entry.delete(0, tk.END)

        # Videoları yükle
        load_channel_videos(url)

    except Exception as e:
        messagebox.showerror("Hata", f"Kanal eklenirken bir hata oluştu: {e}")

def load_saved_channels():
    try:
        channels_data = load_channels_data()
        for url, data in channels_data.items():
            channel_name = data.get('name', '')
            mail = data.get('mail', '')
            if channel_name:
                create_channel_button(url, channel_name, mail)

        # Canvas'ı güncelle
        channels_buttons_frame.update_idletasks()
        channels_canvas.configure(scrollregion=channels_canvas.bbox('all'))
    except Exception as e:
        print(f"Kayıtlı kanalları yükleme hatası: {e}")

def check_video_ranks():
    try:
        # Ranks verilerini yükle
        ranks_data = load_ranks_data()
        # Kanal videolarını yükle
        channel_videos_data = load_channel_videos_data()

        # Güncellenecek kanal adını bul
        current_channel = None
        selected_tab = notebook.select()
        tab_text = notebook.tab(selected_tab, "text")

        if tab_text == "Kanallar" and videos_tree.get_children():
            # İlk videonun URL'sinden kanal adını bulmaya çalış
            first_item = videos_tree.get_children()[0]
            first_url = videos_tree.item(first_item)['values'][2]  # URL 3. sütunda (index 2)

            # Kanal adını bul
            for ch_name, videos in channel_videos_data.items():
                for video in videos:
                    if video['url'] == first_url:
                        current_channel = ch_name
                        break
                if current_channel:
                    break

        # Treeview'daki tüm videoları kontrol et
        for item in videos_tree.get_children():
            # Video URL'sini al
            video_url = videos_tree.item(item)['values'][2]  # URL 3. sütunda (index 2)

            # Ranks verilerinde bu URL'yi ara
            if video_url in ranks_data:
                # Rank ve Keyword değerlerini al
                current_rank = ranks_data[video_url].get('Current Rank', '-')
                keyword = ranks_data[video_url].get('Keyword', '-')

                # Mevcut değerleri al
                values = list(videos_tree.item(item)['values'])

                # Keyword ve Rank değerlerini güncelle
                values[3] = keyword  # Keyword 4. sütunda (index 3)
                values[5] = current_rank  # Rank 6. sütunda (index 5)

                # Treeview'ı güncelle
                videos_tree.item(item, values=values)

                # JSON'da da güncelle
                if current_channel:
                    for video in channel_videos_data.get(current_channel, []):
                        if video['url'] == video_url:
                            video['keyword'] = keyword
                            video['rank'] = current_rank
                            break
            else:
                # URL ranks verilerinde yoksa, rank ve keyword değerlerini '-' yap
                values = list(videos_tree.item(item)['values'])
                values[3] = '-'  # Keyword
                values[5] = '-'  # Rank
                videos_tree.item(item, values=values)

        # Değişiklikleri JSON'a kaydet
        if current_channel and current_channel in channel_videos_data:
            with open(CHANNEL_VIDEOS_FILE, 'w', encoding='utf-8') as f:
                json.dump(channel_videos_data, f, ensure_ascii=False, indent=4)
            print(f"Video bilgileri güncellendi ve kaydedildi.")

    except Exception as e:
        print(f"Rank kontrolü hatası: {e}")

def load_channel_videos(channel_url):
    try:
        # Önce kayıtlı videoları kontrol et
        channel_name = channel_url.split('@')[1].split('/')[0] if '@' in channel_url else channel_url.split('/')[-1]
        saved_data = load_channel_videos_data()

        if channel_name in saved_data:
            # Kayıtlı videoları yükle
            videos_tree.delete(*videos_tree.get_children())
            for video_data in saved_data[channel_name]:
                item = videos_tree.insert('', 'end', values=(
                    CHECK_OFF,  # Seçim kutucuğu için boş değer
                    video_data['thumbnail'],
                    video_data['url'],
                    video_data.get('keyword', '-'),  # Keyword değeri
                    video_data['title'],
                    video_data.get('rank', '-'),  # Rank değeri
                    video_data['likes'],
                    video_data['comments'],
                    video_data['views'],
                    video_data['upload_time'],
                    video_data['daily_change']
                ))
                videos_tree.item(item, tags=('unchecked',))
            print(f"Kayıtlı {len(saved_data[channel_name])} video yüklendi.")
            return

        # Chrome ayarları
        options = webdriver.ChromeOptions()
        options.add_argument('--headless=new')
        options.add_argument('--disable-gpu')
        options.add_argument('--incognito')

        # WebDriver başlat
        service = Service()
        driver = webdriver.Chrome(service=service, options=options)

        # Video listesini temizle
        videos_tree.delete(*videos_tree.get_children())

        # Kanala git
        driver.get(channel_url)
        time.sleep(3)

        # Videoları saklamak için liste
        videos_data = []

        # Scroll işlemi için sayaç
        scroll_count = 0
        max_scrolls = 20

        while scroll_count < max_scrolls:
            videos = driver.find_elements(By.CSS_SELECTOR, 'ytd-rich-item-renderer')
            print(f"Bulunan video sayısı: {len(videos)}")

            for video in videos:
                try:
                    title_element = video.find_element(By.CSS_SELECTOR, '#video-title-link')
                    video_url = title_element.get_attribute('href')
                    title = title_element.get_attribute('title')

                    if not video_url:
                        continue

                    # URL'deki & işaretinden sonraki kısmı temizle
                    if '&' in video_url:
                        video_url = video_url.split('&')[0]

                    thumbnail = video.find_element(By.CSS_SELECTOR, 'yt-image img').get_attribute('src')

                    # İzlenme ve yüklenme zamanı için yeni seçici
                    meta_items = video.find_elements(By.CSS_SELECTOR, 'span.inline-metadata-item')
                    views = meta_items[0].text if len(meta_items) > 0 else "0"
                    upload_time = meta_items[1].text if len(meta_items) > 1 else ""

                    # Video verilerini hazırla
                    video_data = {
                        'thumbnail': thumbnail,
                        'url': video_url.split('&')[0] if '&' in video_url else video_url,  # URL'yi temizle
                        'title': title,
                        'likes': "0",
                        'comments': "0",
                        'views': views,
                        'upload_time': upload_time,
                        'daily_change': "0",
                        'last_update': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }

                    # URL'yi http ile başlatmıyorsa ekle
                    if not video_data['url'].startswith('http'):
                        video_data['url'] = f"https://www.youtube.com{video_data['url']}"

                    # Listeye ekle
                    videos_data.append(video_data)

                    # Treeview'a ekle
                    item = videos_tree.insert('', 'end', values=(
                        CHECK_OFF,  # Seçim kutucuğu için boş değer
                        video_data['thumbnail'],
                        video_data['url'],
                        video_data.get('keyword', '-'),  # Keyword değeri
                        video_data['title'],
                        video_data.get('rank', '-'),  # Rank değeri
                        video_data['likes'],
                        video_data['comments'],
                        video_data['views'],
                        video_data['upload_time'],
                        video_data['daily_change']
                    ))
                    videos_tree.item(item, tags=('unchecked',))

                except Exception as e:
                    print(f"Video bilgisi alma hatası: {e}")
                    continue

            # Scroll yap
            last_height = driver.execute_script("return document.documentElement.scrollHeight")
            driver.execute_script("window.scrollTo(0, document.documentElement.scrollHeight);")
            time.sleep(2)

            new_height = driver.execute_script("return document.documentElement.scrollHeight")
            if new_height == last_height:
                break

            scroll_count += 1

        # Verileri kaydet
        save_channel_videos_data(channel_name, videos_data)

    except Exception as e:
        print(f"Video listesi yükleme hatası: {e}")
        messagebox.showerror("Hata", f"Videolar yüklenirken bir hata oluştu: {e}")
    finally:
        if 'driver' in locals():
            driver.quit()

# Uygulama başladığında kayıtlı kanalları yükle
load_saved_channels()

# Canvas scroll olayları
def on_canvas_configure(event):
    channels_canvas.configure(scrollregion=channels_canvas.bbox('all'))

channels_buttons_frame.bind('<Configure>', on_canvas_configure)
channels_canvas.configure(xscrollcommand=channels_scrollbar.set)

# Video seçim ve URL kopyalama için binding
def toggle_video_selection(event):
    item = videos_tree.identify_row(event.y)
    column = videos_tree.identify_column(event.x)

    if item and column == '#1':  # Seçim kutucuğu sütunu
        current_tags = videos_tree.item(item, 'tags')
        values = list(videos_tree.item(item)['values'])

        if 'checked' in current_tags:
            videos_tree.item(item, tags=('unchecked',))
            values[0] = CHECK_OFF
        else:
            videos_tree.item(item, tags=('checked',))
            values[0] = CHECK_ON

        videos_tree.item(item, values=values)
    elif item and column == '#3':  # URL sütunu
        # URL'yi panoya kopyala
        url = videos_tree.item(item)['values'][2]  # URL 3. sütunda (index 2)
        if url:
            root.clipboard_clear()
            root.clipboard_append(url)
            messagebox.showinfo("Bilgi", "Video URL'si panoya kopyalandı!")

videos_tree.bind('<Button-1>', toggle_video_selection)

# Filtreleme araçları
filter_frame = ttk.LabelFrame(ranks_frame, text="Filtreleme ve Sıralama", style='Custom.TFrame')
filter_frame.pack(fill=tk.X, padx=5, pady=5)

# Sıralama seçenekleri
sort_label = ttk.Label(filter_frame, text="Sırala:", style='Custom.TLabel')
sort_label.pack(side=tk.LEFT, padx=5)

sort_options = [
    ("Rank", "rank"),
    ("İzlenme Artışı", "views"),
    ("Son Güncelleme", "update")
]

for text, value in sort_options:
    ttk.Radiobutton(filter_frame, text=text, value=value, variable=sort_var, style='Custom.TRadiobutton').pack(side=tk.LEFT, padx=5)

# Tag filtresi
tag_label = ttk.Label(filter_frame, text="Tag Filtresi:", style='Custom.TLabel')
tag_label.pack(side=tk.LEFT, padx=(20, 5))
tag_filter = ttk.Entry(filter_frame, style='Custom.TEntry', width=20)
tag_filter.pack(side=tk.LEFT, padx=5)

# TARA butonu
scan_button = ttk.Button(
    filter_frame,
    text="TARA",
    style='Custom.TButton',
    command=lambda: scan_all_urls_and_keywords()
)
scan_button.pack(side=tk.RIGHT, padx=20)

# Toplu Sorgu sekmesi içeriği
bulk_label = ttk.Label(
    bulk_frame,
    text="Sorguları alt alta girin (Format: URL | Anahtar Kelime)",
    style='Custom.TLabel',
    font=('Segoe UI', 12)
)
bulk_label.pack(pady=10)

bulk_text = tk.Text(bulk_frame, height=20, width=80, font=('Segoe UI', 10))
bulk_text.pack(pady=10, padx=20)

bulk_button = ttk.Button(
    bulk_frame,
    text="Toplu Tara",
    style='Custom.TButton',
    command=process_bulk_search
)
bulk_button.pack(pady=10)

# Örnek format
example_label = ttk.Label(
    bulk_frame,
    text="Örnek:\nhttps://youtube.com/watch?v=123 | anahtar kelime 1\nhttps://youtube.com/watch?v=456 | anahtar kelime 2",
    style='Custom.TLabel',
    justify=tk.LEFT
)
example_label.pack(pady=10)

# Ana sayfa içeriği
title_label = ttk.Label(
    main_frame,
    text="YouTube Video Sıralama Analizi",
    style='Custom.TLabel',
    font=('Segoe UI', 24, 'bold')
)
title_label.pack(pady=(0, 20))

# Giriş alanları frame
input_frame = ttk.Frame(main_frame, style='Custom.TFrame')
input_frame.pack(fill=tk.X, padx=20)

# URL giriş alanı
url_label = ttk.Label(input_frame, text="YouTube Video URL:", style='Custom.TLabel')
url_label.pack(anchor='w')
url_entry = ttk.Entry(input_frame, style='Custom.TEntry', width=70)
url_entry.pack(fill=tk.X, pady=(0, 10))

# Keyword giriş alanı
keyword_label = ttk.Label(input_frame, text="Anahtar Kelime:", style='Custom.TLabel')
keyword_label.pack(anchor='w')
keyword_entry = ttk.Entry(input_frame, style='Custom.TEntry', width=70)
keyword_entry.pack(fill=tk.X, pady=(0, 20))

# Progress bar
progress_var = tk.DoubleVar()
progress_bar = ttk.Progressbar(
    input_frame,
    variable=progress_var,
    maximum=100,
    mode='determinate',
    length=300
)
progress_bar.pack(pady=(0, 10))

# Arama butonu
search_button = ttk.Button(
    input_frame,
    text="Ara",
    style='Custom.TButton',
    command=search_video
)
search_button.pack(pady=(0, 20))

# Sonuçlar frame
results_frame = ttk.LabelFrame(main_frame, text="Sonuçlar", style='Custom.TFrame')
results_frame.pack(fill=tk.X, padx=20, pady=(0, 20))

results_text = tk.Text(results_frame, height=4, font=('Segoe UI', 10))
results_text.pack(fill=tk.X, padx=5, pady=5)

# Geçmiş frame
history_frame = ttk.LabelFrame(main_frame, text="Geçmiş Aramalar", style='Custom.TFrame')
history_frame.pack(fill=tk.BOTH, expand=True, padx=20)

# Treeview için columns
columns = ('Sıra', 'Anahtar Kelime', 'URL', 'İzlenme Sayısı', 'Yüklenme Zamanı')
history_tree = ttk.Treeview(history_frame, columns=columns, show='headings')

# Column headings
for col in columns:
    history_tree.heading(col, text=col)
    if col == 'URL':
        history_tree.column(col, width=300)
    elif col in ('İzlenme Sayısı', 'Yüklenme Zamanı'):
        history_tree.column(col, width=150)
    else:
        history_tree.column(col, width=100)

history_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

# Scrollbar ekle
scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=history_tree.yview)
scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
history_tree.configure(yscrollcommand=scrollbar.set)

# Alt frame (stop butonu için)
bottom_frame = ttk.Frame(main_frame, style='Custom.TFrame')
bottom_frame.pack(fill=tk.X, padx=20, pady=10)

# Stop butonu
stop_button = ttk.Button(
    bottom_frame,
    text="Durdur",
    style='Custom.TButton',
    command=stop_search_process,
    state='disabled'
)
stop_button.pack(side=tk.RIGHT, padx=5)

# Custom style oluştur
create_custom_style()

# Treeview ve scrollbar'ları oluştur
ranks_tree_frame = ttk.Frame(ranks_frame)
ranks_tree_frame.pack(fill='both', expand=True, padx=5, pady=5)

ranks_tree_scroll_y = ttk.Scrollbar(ranks_tree_frame)
ranks_tree_scroll_y.pack(side='right', fill='y')

ranks_tree_scroll_x = ttk.Scrollbar(ranks_tree_frame, orient='horizontal')
ranks_tree_scroll_x.pack(side='bottom', fill='x')

# Silme butonu için yeni sütun ekle
all_columns = ['Delete'] + ranks_columns
ranks_tree = ttk.Treeview(ranks_tree_frame,
                         columns=all_columns,
                         show='headings',
                         yscrollcommand=ranks_tree_scroll_y.set,
                         xscrollcommand=ranks_tree_scroll_x.set)

ranks_tree_scroll_y.config(command=ranks_tree.yview)
ranks_tree_scroll_x.config(command=ranks_tree.xview)

# Silme butonu sütunu
ranks_tree.heading('Delete', text='')
ranks_tree.column('Delete', width=30, anchor='center')

# Diğer sütunları ayarla
for col in ranks_columns:
    ranks_tree.heading(col, text=col, command=lambda c=col: treeview_sort_column(ranks_tree, c, False))
    ranks_tree.column(col, width=column_widths.get(col, 100), minwidth=50)

ranks_tree.pack(fill='both', expand=True)

def copy_item(event):
    try:
        item = ranks_tree.selection()[0]
        column = ranks_tree.identify_column(event.x)
        col_id = ranks_tree.column(column)['id']
        if col_id != 'Delete':  # Delete sütunu hariç
            value = ranks_tree.item(item)['values'][all_columns.index(col_id)]
            root.clipboard_clear()
            root.clipboard_append(value)
    except Exception as e:
        print(f"Kopyalama hatası: {e}")

def delete_record(event):
    try:
        item = ranks_tree.identify_row(event.y)
        column = ranks_tree.identify_column(event.x)

        if item and column == '#1':  # Silme butonu sütunu
            values = ranks_tree.item(item)['values']
            if values:
                url = values[1]  # URL ikinci sütunda

                # Kullanıcıya sor
                if messagebox.askyesno("Silme Onayı", "Bu kaydı silmek istediğinizden emin misiniz?"):
                    # Google Sheets'ten sil
                    worksheet = ranks_sheet
                    cell = worksheet.find(url)
                    if cell:
                        worksheet.delete_rows(cell.row)

                    # Yerel verilerden sil
                    ranks_data = load_ranks_data()
                    if url in ranks_data:
                        del ranks_data[url]
                        save_ranks_data(ranks_data)

                    # Treeview'dan sil
                    ranks_tree.delete(item)
                    messagebox.showinfo("Bilgi", "Kayıt başarıyla silindi!")
    except Exception as e:
        print(f"Silme hatası: {e}")

def edit_tag(event):
    try:
        item = ranks_tree.selection()[0]
        column = ranks_tree.identify_column(event.x)
        col_id = ranks_tree.column(column)['id']

        # Tag ve Channel sütunlarında düzenlemeye izin ver
        if col_id in ['Tag', 'Channel']:
            x, y, w, h = ranks_tree.bbox(item, column)
            values = ranks_tree.item(item)['values']
            url = values[1]  # URL ikinci sütunda

            def save_tag(event=None):
                try:
                    if not edit_entry.winfo_exists():
                        return

                    new_value = edit_entry.get()
                    ranks_data = load_ranks_data()

                    if url in ranks_data:
                        # Hangi sütunun düzenlendiğini belirle
                        field = col_id
                        ranks_data[url][field] = new_value
                        save_ranks_data(ranks_data)
                        update_ranks_table()

                except Exception as e:
                    print(f"Değer kaydetme hatası: {e}")
                finally:
                    if edit_entry.winfo_exists():
                        edit_entry.destroy()

            edit_entry = ttk.Entry(ranks_tree, style='Custom.TEntry')
            edit_entry.place(x=x, y=y, width=w, height=h)

            # Mevcut değeri göster
            ranks_data = load_ranks_data()
            current_value = ranks_data.get(url, {}).get(col_id, '')
            edit_entry.insert(0, current_value)

            edit_entry.select_range(0, tk.END)
            edit_entry.focus()
            edit_entry.bind('<Return>', save_tag)
            edit_entry.bind('<FocusOut>', save_tag)

    except Exception as e:
        print(f"Düzenleme hatası: {e}")

# Mouse binding'leri
ranks_tree.bind('<Button-1>', lambda e: delete_record(e) if ranks_tree.identify_column(e.x) == '#1' else (
    root.clipboard_clear() or root.clipboard_append(ranks_tree.item(ranks_tree.identify_row(e.y))['values'][1])
    if ranks_tree.identify_column(e.x) == '#2'
    else None
))
ranks_tree.bind('<Double-Button-1>', edit_tag)

# Silme butonu için stil
style = ttk.Style()
style.configure("Treeview", rowheight=25)
style.configure("Treeview.Heading", font=('Calibri', 10, 'bold'))
style.map("Treeview",
          foreground=[('selected', '#000000')],
          background=[('selected', '#f0f0f0')])

# Delete sütunu için özel renk
ranks_tree.tag_configure('delete_button', foreground='red')

# Ranks sekmesindeki tüm URL ve Keyword bilgilerini Toplu Sorgu sekmesine aktarma fonksiyonu
def scan_all_urls_and_keywords():
    try:
        # Ranks tablosundaki tüm verileri al
        all_items = ranks_tree.get_children()
        if not all_items:
            messagebox.showinfo("Bilgi", "Ranks tablosunda veri bulunamadı!")
            return

        # URL ve Keyword bilgilerini topla
        bulk_data = []
        for item in all_items:
            values = ranks_tree.item(item)['values']
            if len(values) >= 3:  # En az 3 sütun olmalı (Delete, URL, Keyword)
                url = values[1]  # URL ikinci sütunda
                keyword = values[2]  # Keyword üçüncü sütunda
                if url and keyword:
                    bulk_data.append(f"{url} | {keyword}")

        if not bulk_data:
            messagebox.showinfo("Bilgi", "İşlenecek URL ve Keyword bulunamadı!")
            return

        # Toplu Sorgu sekmesine geç
        notebook.select(1)  # Toplu Sorgu sekmesi (index 1)

        # Toplu Sorgu metin alanını temizle ve verileri ekle
        bulk_text.delete(1.0, tk.END)
        bulk_text.insert(tk.END, "\n".join(bulk_data))

        # Toplu Tara butonuna tıkla
        bulk_button.invoke()

        messagebox.showinfo("Bilgi", f"{len(bulk_data)} adet URL ve Keyword taranmak üzere aktarıldı!")

    except Exception as e:
        messagebox.showerror("Hata", f"Tarama işlemi sırasında bir hata oluştu: {e}")

# Güncel sekmesi için fonksiyonlar
def scrape_channel_views(channel_url):
    """Kanal toplam izlenme sayısını scrape eder"""
    try:
        options = webdriver.ChromeOptions()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')

        driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)

        # Kanal hakkında sayfasına git
        about_url = channel_url.rstrip('/') + '/about'
        driver.get(about_url)
        time.sleep(3)

        # Toplam görüntüleme sayısını bul
        try:
            # Farklı CSS selector'ları dene
            selectors = [
                'td.style-scope.ytd-about-channel-renderer',
                '.about-stats .style-scope.ytd-about-channel-renderer',
                '[class*="about-stats"] td',
                'yt-formatted-string[class*="style-scope"]'
            ]

            views_text = ""
            for selector in selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        text = element.text.strip()
                        if 'görüntüleme' in text.lower() or 'views' in text.lower():
                            views_text = text
                            break
                    if views_text:
                        break
                except:
                    continue

            # Sayıyı çıkar (örn: "5.506 görüntüleme" -> 5506)
            if views_text and ('görüntüleme' in views_text.lower() or 'views' in views_text.lower()):
                # Sayıları çıkar
                import re
                numbers = re.findall(r'[\d.,]+', views_text)
                if numbers:
                    views_number = numbers[0].replace('.', '').replace(',', '').strip()
                    return int(views_number) if views_number.isdigit() else 0
        except Exception as e:
            print(f"Element bulma hatası: {e}")
            pass

        driver.quit()
        return 0

    except Exception as e:
        print(f"Scraping hatası: {e}")
        return 0

def load_daily_views_data():
    """Günlük izlenme verilerini yükler"""
    try:
        if os.path.exists('daily_views.json'):
            with open('daily_views.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    except Exception as e:
        print(f"Günlük veri yükleme hatası: {e}")
        return {}

def save_daily_views_data(data):
    """Günlük izlenme verilerini kaydeder"""
    try:
        with open('daily_views.json', 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
    except Exception as e:
        print(f"Günlük veri kaydetme hatası: {e}")

def update_channel_views():
    """Tüm kanalların izlenme sayılarını günceller"""
    try:
        channels_data = load_channels_data()
        daily_data = load_daily_views_data()
        today = datetime.now().strftime("%Y-%m-%d")

        if today not in daily_data:
            daily_data[today] = {}

        total_views_today = 0
        channel_count = len(channels_data)

        if channel_count == 0:
            messagebox.showinfo("Bilgi", "Güncellenecek kanal bulunamadı!")
            return

        # Progress bar'ı başlat
        guncel_progress_var.set(0)
        info_label.config(text="Kanallar güncelleniyor...")
        root.update()

        for i, (channel_url, channel_info) in enumerate(channels_data.items()):
            channel_name = channel_info.get('name', '')
            if channel_name:
                # Progress bar'ı güncelle
                progress = (i / channel_count) * 100
                guncel_progress_var.set(progress)
                info_label.config(text=f"Güncelleniyor: {channel_name}")
                root.update()

                # Kanal izlenme sayısını scrape et
                views = scrape_channel_views(channel_url)
                daily_data[today][channel_name] = views
                total_views_today += views

        # Toplam izlenmeyi kaydet
        daily_data[today]['TOPLAM'] = total_views_today
        save_daily_views_data(daily_data)

        # Progress bar'ı tamamla
        guncel_progress_var.set(100)
        info_label.config(text="Güncelleme tamamlandı!")
        root.update()

        # Tabloyu güncelle
        update_guncel_table()

        # Progress bar'ı sıfırla
        root.after(2000, lambda: (guncel_progress_var.set(0), info_label.config(text="Son 7 günün kanal izlenme verileri")))

        messagebox.showinfo("Bilgi", f"{channel_count} kanal güncellendi!\nToplam izlenme: {total_views_today:,}")

    except Exception as e:
        guncel_progress_var.set(0)
        info_label.config(text="Hata oluştu!")
        messagebox.showerror("Hata", f"Güncelleme sırasında hata: {e}")

def update_guncel_table():
    """Güncel tablosunu günceller"""
    try:
        # Tabloyu temizle
        for item in guncel_tree.get_children():
            guncel_tree.delete(item)

        daily_data = load_daily_views_data()
        channels_data = load_channels_data()

        # Son 7 günün verilerini al
        dates = sorted(daily_data.keys())[-7:]

        # Sütun başlıklarını güncelle (tarihleri göster)
        if dates:
            # Sütun başlıklarını yeniden ayarla
            new_columns = ['Kanal']
            for date in dates:
                # Tarihi kısa formatta göster (MM-DD)
                try:
                    date_obj = datetime.strptime(date, "%Y-%m-%d")
                    short_date = date_obj.strftime("%m-%d")
                    new_columns.append(short_date)
                except:
                    new_columns.append(date[-5:])  # Son 5 karakter (MM-DD)
            new_columns.append('Değişim')

            # Sütun başlıklarını güncelle
            for i, col in enumerate(new_columns):
                if i < len(guncel_tree['columns']):
                    guncel_tree.heading(guncel_tree['columns'][i], text=col)

        # Her kanal için satır ekle
        for channel_url, channel_info in channels_data.items():
            channel_name = channel_info.get('name', '')
            if channel_name:
                row_data = [channel_name]

                # Son 7 günün verilerini ekle
                for date in dates:
                    views = daily_data.get(date, {}).get(channel_name, 0)
                    row_data.append(f"{views:,}")

                # Boş sütunları doldur (7 günden az veri varsa)
                while len(row_data) < 8:  # 1 kanal + 7 gün
                    row_data.append("0")

                # Değişim hesapla (son 2 gün)
                if len(dates) >= 2:
                    yesterday_views = daily_data.get(dates[-2], {}).get(channel_name, 0)
                    today_views = daily_data.get(dates[-1], {}).get(channel_name, 0)
                    change = today_views - yesterday_views
                    change_text = f"+{change:,}" if change > 0 else f"{change:,}"
                    row_data.append(change_text)
                else:
                    row_data.append("0")

                guncel_tree.insert('', 'end', values=row_data)

        # Toplam satırı ekle
        if dates:
            total_row = ['TOPLAM']
            for date in dates:
                total_views = daily_data.get(date, {}).get('TOPLAM', 0)
                total_row.append(f"{total_views:,}")

            # Boş sütunları doldur
            while len(total_row) < 8:
                total_row.append("0")

            # Toplam değişim
            if len(dates) >= 2:
                yesterday_total = daily_data.get(dates[-2], {}).get('TOPLAM', 0)
                today_total = daily_data.get(dates[-1], {}).get('TOPLAM', 0)
                total_change = today_total - yesterday_total
                change_text = f"+{total_change:,}" if total_change > 0 else f"{total_change:,}"
                total_row.append(change_text)
            else:
                total_row.append("0")

            guncel_tree.insert('', 'end', values=total_row, tags=('total',))

        # Toplam satırını vurgula
        guncel_tree.tag_configure('total', background='#e6f3ff', font=('Segoe UI', 10, 'bold'))

    except Exception as e:
        print(f"Tablo güncelleme hatası: {e}")

# Güncel sekmesi UI bileşenlerini oluştur
# Güncel sekmesi için üst frame (butonlar)
guncel_top_frame = ttk.Frame(guncel_frame, style='Custom.TFrame')
guncel_top_frame.pack(fill=tk.X, padx=10, pady=10)

# Güncelle butonu
update_views_button = ttk.Button(
    guncel_top_frame,
    text="İzlenme Sayılarını Güncelle",
    style='Custom.TButton',
    command=update_channel_views
)
update_views_button.pack(side=tk.LEFT, padx=5)

# Yenile butonu
refresh_table_button = ttk.Button(
    guncel_top_frame,
    text="Tabloyu Yenile",
    style='Custom.TButton',
    command=update_guncel_table
)
refresh_table_button.pack(side=tk.LEFT, padx=5)

# Progress bar için frame
progress_frame = ttk.Frame(guncel_top_frame, style='Custom.TFrame')
progress_frame.pack(side=tk.RIGHT, padx=10)

# Progress bar
guncel_progress_var = tk.DoubleVar()
guncel_progress_bar = ttk.Progressbar(
    progress_frame,
    variable=guncel_progress_var,
    maximum=100,
    mode='determinate',
    length=200
)
guncel_progress_bar.pack(side=tk.TOP, pady=2)

# Bilgi etiketi
info_label = ttk.Label(
    progress_frame,
    text="Son 7 günün kanal izlenme verileri",
    style='Custom.TLabel',
    font=('Segoe UI', 9, 'italic')
)
info_label.pack(side=tk.TOP)

# Güncel tablosu için frame
guncel_table_frame = ttk.Frame(guncel_frame, style='Custom.TFrame')
guncel_table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

# Güncel tablosu için scrollbar'lar
guncel_scroll_y = ttk.Scrollbar(guncel_table_frame)
guncel_scroll_y.pack(side='right', fill='y')

guncel_scroll_x = ttk.Scrollbar(guncel_table_frame, orient='horizontal')
guncel_scroll_x.pack(side='bottom', fill='x')

# Güncel tablosu sütunları (dinamik olarak son 7 gün + değişim)
guncel_columns = ['Kanal', 'Gün-7', 'Gün-6', 'Gün-5', 'Gün-4', 'Gün-3', 'Gün-2', 'Bugün', 'Değişim']

# Güncel tablosu
guncel_tree = ttk.Treeview(
    guncel_table_frame,
    columns=guncel_columns,
    show='headings',
    yscrollcommand=guncel_scroll_y.set,
    xscrollcommand=guncel_scroll_x.set
)

guncel_scroll_y.config(command=guncel_tree.yview)
guncel_scroll_x.config(command=guncel_tree.xview)

# Sütun başlıklarını ayarla
for col in guncel_columns:
    guncel_tree.heading(col, text=col)
    if col == 'Kanal':
        guncel_tree.column(col, width=150, minwidth=100)
    elif col == 'Değişim':
        guncel_tree.column(col, width=100, minwidth=80)
    else:
        guncel_tree.column(col, width=120, minwidth=80)

guncel_tree.pack(fill='both', expand=True)

# Uygulama başladığında ve sekme değiştiğinde Ranks tablosunu yükle
def on_notebook_tab_changed(event):
    try:
        selected_tab = event.widget.select()
        tab_text = event.widget.tab(selected_tab, "text")
        if tab_text == "Ranks":
            update_ranks_table()
        elif tab_text == "Güncel":
            update_guncel_table()
    except Exception as e:
        print(f"Sekme değişim hatası: {e}")

# Sekme değiştiğinde tabloyu güncelle
notebook.bind('<<NotebookTabChanged>>', on_notebook_tab_changed)

# Sıralama ve filtreleme değişikliklerinde tabloyu güncelle
sort_var.trace_add('write', lambda *args: update_ranks_table())
tag_filter.bind('<KeyRelease>', lambda *args: update_ranks_table())

# İlk yükleme
update_ranks_table()

# Uygulama penceresini başlat
root.mainloop()