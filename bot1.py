import tkinter as tk
from tkinter import ttk, messagebox, Menu
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import gspread
from google.oauth2.service_account import Credentials
from datetime import datetime, timedelta
import json
import os

# Kanal verilerini saklamak için JSON dosyası
CHANNELS_DATA_FILE = 'channels_data.json'
CHANNEL_VIDEOS_FILE = 'channel_videos.json'

def clean_saved_video_urls():
    try:
        # Kanal videolarını yükle
        if os.path.exists(CHANNEL_VIDEOS_FILE):
            with open(CHANNEL_VIDEOS_FILE, 'r', encoding='utf-8') as f:
                channels_data = json.load(f)

            # Her kanal için
            data_changed = False
            for channel_name, videos in channels_data.items():
                # Her video için
                for video in videos:
                    # URL'yi temizle
                    original_url = video['url']
                    cleaned_url = original_url.split('&')[0] if '&' in original_url else original_url

                    # Eğer URL değiştiyse
                    if cleaned_url != original_url:
                        video['url'] = cleaned_url
                        data_changed = True

            # Eğer değişiklik yapıldıysa kaydet
            if data_changed:
                with open(CHANNEL_VIDEOS_FILE, 'w', encoding='utf-8') as f:
                    json.dump(channels_data, f, ensure_ascii=False, indent=4)
                print("Kayıtlı video URL'leri temizlendi.")

    except Exception as e:
        print(f"URL temizleme hatası: {e}")

def load_channel_videos_data():
    try:
        if os.path.exists(CHANNEL_VIDEOS_FILE):
            with open(CHANNEL_VIDEOS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    except Exception as e:
        print(f"Kanal videoları yükleme hatası: {e}")
        return {}

def save_channel_videos_data(channel_name, videos_data):
    try:
        # Mevcut verileri yükle
        all_data = load_channel_videos_data()

        # Kanal verilerini güncelle
        all_data[channel_name] = videos_data

        # JSON dosyasına kaydet
        with open(CHANNEL_VIDEOS_FILE, 'w', encoding='utf-8') as f:
            json.dump(all_data, f, ensure_ascii=False, indent=4)

        print(f"{channel_name} kanalı için {len(videos_data)} video kaydedildi.")
    except Exception as e:
        print(f"Kanal videoları kaydetme hatası: {e}")

# Ana pencere oluşturma ve özelleştirme
root = tk.Tk()
root.title("YouTube Video Sıralama Analizi")
root.geometry("1000x800")
root.configure(bg='white')

# Başlangıçta URL'leri temizle
clean_saved_video_urls()

# Checkbox için metin sembolleri tanımla
CHECK_ON = "☑"
CHECK_OFF = "☐"

# Google Sheets bağlantısı için try-except bloğu ekleyelim
try:
    # Google Sheets API için kimlik bilgilerini yükleyin
    scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/spreadsheets"]
    creds = Credentials.from_service_account_file('credentials.json', scopes=scope)
    client = gspread.authorize(creds)

    # Sütun tanımlamaları
    ranks_columns = ['URL', 'Keyword', 'Tag', 'Channel', 'Current Rank', 'Previous Rank', 'Current Views',
                    'Previous Views', 'Views Difference', 'Upload Time', 'Last Update', 'Rank Change']

    column_widths = {
        'URL': 300,
        'Keyword': 150,
        'Tag': 100,
        'Channel': 150,
        'Current Rank': 100,
        'Previous Rank': 100,
        'Current Views': 100,
        'Previous Views': 100,
        'Views Difference': 100,
        'Upload Time': 150,
        'Last Update': 150,
        'Rank Change': 80
    }

    # Google Sheet dosyasına bağlanın
    spreadsheet = client.open("youtube rank checker")
    sheet = spreadsheet.sheet1  # İlk çalışma sayfasını seçin

    # Ranks sayfasını kontrol et ve oluştur
    try:
        ranks_sheet = spreadsheet.worksheet("Ranks")
    except gspread.WorksheetNotFound:
        ranks_sheet = spreadsheet.add_worksheet(title="Ranks", rows="1000", cols="20")

    # Ranks sayfası sütun başlıklarını kontrol et ve güncelle
    ranks_headers = ranks_sheet.row_values(1)
    ranks_required_headers = ['URL', 'Keyword', 'Tag', 'Channel', 'Current Rank', 'Previous Rank', 'Current Views', 'Previous Views',
                            'Views Difference', 'Upload Time', 'Last Update', 'Rank Change']
    if not ranks_headers:
        ranks_sheet.append_row(ranks_required_headers)
    elif ranks_headers != ranks_required_headers:
        ranks_sheet.clear()
        ranks_sheet.append_row(ranks_required_headers)

    # Ana sayfa sütun başlıklarını kontrol et ve güncelle
    headers = sheet.row_values(1)
    required_headers = ['Rank', 'Keyword', 'URL', 'Views', 'Upload Time', 'New Rank', 'New Views', 'Last Update', 'Views Difference']
    if not headers:
        sheet.append_row(required_headers)
    elif headers != required_headers:
        sheet.clear()
        sheet.append_row(required_headers)
except Exception as e:
    print(f"Google Sheets bağlantı hatası: {e}")
    # Hata durumunda boş değerler ata
    spreadsheet = None
    sheet = None
    ranks_sheet = None

# Ranks verilerini saklamak için JSON dosyası
RANKS_DATA_FILE = 'ranks_data.json'

# Ranks verilerini yükle
def load_ranks_data():
    try:
        if os.path.exists(RANKS_DATA_FILE):
            with open(RANKS_DATA_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    except Exception as e:
        print(f"Ranks veri yükleme hatası: {e}")
        return {}

# Ranks verilerini kaydet
def save_ranks_data(data):
    try:
        with open(RANKS_DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
    except Exception as e:
        print(f"Ranks veri kaydetme hatası: {e}")

def update_ranks_cell_color(row_number, old_rank, new_rank):
    worksheet_id = ranks_sheet._properties['sheetId']

    # Renk tanımlamaları
    green_color = {"red": 0.8, "green": 1, "blue": 0.8}    # Açık yeşil
    red_color = {"red": 1, "green": 0.8, "blue": 0.8}      # Açık kırmızı
    gray_color = {"red": 0.9, "green": 0.9, "blue": 0.9}   # Açık gri

    try:
        old_rank = int(old_rank) if old_rank else 999
        new_rank = int(new_rank) if new_rank else 999

        # Renk seçimi
        if new_rank < old_rank:  # İyileşme
            background_color = green_color
            change_text = "↑"
        elif new_rank > old_rank:  # Kötüleşme
            background_color = red_color
            change_text = "↓"
        else:  # Değişim yok
            background_color = gray_color
            change_text = "="

        # Rank Change sütununu güncelle
        ranks_sheet.update_cell(row_number, 11, change_text)

        # Hücre formatını güncelle
        format_request = {
            "requests": [
                {
                    "updateCells": {
                        "rows": [
                            {
                                "values": [
                                    {
                                        "userEnteredFormat": {
                                            "backgroundColor": background_color
                                        }
                                    }
                                ]
                            }
                        ],
                        "fields": "userEnteredFormat.backgroundColor",
                        "range": {
                            "sheetId": worksheet_id,
                            "startRowIndex": row_number - 1,
                            "endRowIndex": row_number,
                            "startColumnIndex": 3,  # Current Rank sütunu
                            "endColumnIndex": 4
                        }
                    }
                }
            ]
        }

        spreadsheet.batch_update(format_request)
    except Exception as e:
        print(f"Renk güncelleme hatası: {e}")

def update_ranks_record(url, keyword, video_position, video_views, video_upload_time):
    try:
        # Mevcut verileri yükle
        ranks_data = load_ranks_data()
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        if url in ranks_data:
            # Mevcut kaydı güncelle
            current_data = ranks_data[url]

            # Önceki değerleri sakla
            current_data['Previous Rank'] = current_data.get('Current Rank', '')
            current_data['Previous Views'] = current_data.get('Current Views', '')

            # Yeni değerleri güncelle
            current_data['Current Rank'] = str(video_position)
            current_data['Current Views'] = str(video_views)
            current_data['Last Update'] = current_time

            # Views farkını hesapla
            try:
                old_views = int(str(current_data['Previous Views']).replace(',', '')) if current_data['Previous Views'] else 0
                new_views = int(str(video_views).replace(',', '')) if video_views != "Bilinmiyor" else 0
                current_data['Views Difference'] = str(new_views - old_views)
            except:
                current_data['Views Difference'] = "0"

            # Rank değişimini hesapla
            try:
                old_rank = int(current_data['Previous Rank']) if current_data['Previous Rank'] else 999
                new_rank = int(video_position) if str(video_position).isdigit() else 999
                if new_rank < old_rank:
                    current_data['Rank Change'] = "↑"
                elif new_rank > old_rank:
                    current_data['Rank Change'] = "↓"
                else:
                    current_data['Rank Change'] = "="
            except:
                current_data['Rank Change'] = "="

        else:
            # Yeni kayıt oluştur
            ranks_data[url] = {
                'URL': url,
                'Keyword': keyword,
                'Tag': '',
                'Channel': '',
                'Current Rank': str(video_position),
                'Previous Rank': '',
                'Current Views': str(video_views),
                'Previous Views': '',
                'Views Difference': '0',
                'Upload Time': video_upload_time,
                'Last Update': current_time,
                'Rank Change': '='
            }

        # Verileri kaydet
        save_ranks_data(ranks_data)

        # Tabloyu güncelle
        update_ranks_table()

    except Exception as e:
        print(f"Ranks kayıt güncelleme hatası: {e}")

def update_cell_color(row_number, old_rank, new_rank):
    worksheet_id = sheet._properties['sheetId']

    # Renk tanımlamaları
    green_color = {"red": 0.8, "green": 1, "blue": 0.8}    # Açık yeşil
    red_color = {"red": 1, "green": 0.8, "blue": 0.8}      # Açık kırmızı
    gray_color = {"red": 0.9, "green": 0.9, "blue": 0.9}   # Açık gri

    # Renk seçimi
    if int(new_rank) < int(old_rank):  # İyileşme
        background_color = green_color
    elif int(new_rank) > int(old_rank):  # Kötüleşme
        background_color = red_color
    else:  # Değişim yok
        background_color = gray_color

    # Hücre formatını güncelle
    format_request = {
        "requests": [
            {
                "updateCells": {
                    "rows": [
                        {
                            "values": [
                                {
                                    "userEnteredFormat": {
                                        "backgroundColor": background_color
                                    }
                                }
                            ]
                        }
                    ],
                    "fields": "userEnteredFormat.backgroundColor",
                    "range": {
                        "sheetId": worksheet_id,
                        "startRowIndex": row_number - 1,
                        "endRowIndex": row_number,
                        "startColumnIndex": 5,
                        "endColumnIndex": 6
                    }
                }
            }
        ]
    }

    try:
        spreadsheet.batch_update(format_request)
    except Exception as e:
        print(f"Renk güncelleme hatası: {e}")

# Geçmiş sorguları kaydetmek için liste
search_history = []
# Durdurma kontrolü için global değişken
stop_search = False

# Sıralama için gerekli değişkenler
sort_column = None
sort_reverse = False

def treeview_sort_column(tv, col, reverse):
    try:
        # Mevcut verileri al
        l = [(tv.set(k, col), k) for k in tv.get_children('')]

        # Sayısal sütunlar için özel sıralama
        if col in ['Current Rank', 'Previous Rank', 'Current Views', 'Previous Views', 'Views Difference']:
            if col == 'Current Rank':
                # Current Rank için özel sıralama (0 en büyük değer olarak)
                l = [(float('inf') if val.strip() == '0' else float(val.replace(',', '')) if val.strip() and val.strip() != 'Bulunamadı' else float('inf'), k) for val, k in l]
            else:
                l = [(float(val.replace(',', '')) if val.strip() and val.strip() != 'Bulunamadı' else float('inf'), k) for val, k in l]

        # Tarih sütunları için özel sıralama
        elif col in ['Upload Time', 'Last Update']:
            l = [(val if val.strip() and val.strip() != 'Bulunamadı' else '9999-99-99', k) for val, k in l]

        # Sıralama yap
        l.sort(reverse=reverse)

        # Sıralanmış öğeleri yeniden yerleştir
        for index, (val, k) in enumerate(l):
            tv.move(k, '', index)

        # Bir sonraki tıklama için sıralama yönünü değiştir
        tv.heading(col, command=lambda: treeview_sort_column(tv, col, not reverse))

    except Exception as e:
        print(f"Sıralama hatası: {e}")

# Treeview stil tanımlamaları
style = ttk.Style()
style.configure("Treeview", rowheight=25)
style.configure("Treeview.Heading", font=('Calibri', 10, 'bold'))
style.map("Treeview",
          foreground=[('selected', '#000000')],
          background=[('selected', '#f0f0f0')])

def create_custom_style():
    style = ttk.Style()
    style.theme_use('clam')  # Modern tema kullanımı

    # Ana renk paleti
    primary_color = "#2196F3"  # Material Design Blue
    secondary_color = "#757575"  # Material Design Gray
    bg_color = "#FFFFFF"  # White background

    # Entry style
    style.configure('Custom.TEntry',
                   padding=10,
                   fieldbackground=bg_color,
                   borderwidth=1)

    # Button style
    style.configure('Custom.TButton',
                   padding=(20, 10),
                   background=primary_color,
                   foreground='white',
                   font=('Segoe UI', 10, 'bold'))

    # Label style
    style.configure('Custom.TLabel',
                   padding=5,
                   font=('Segoe UI', 10),
                   foreground=secondary_color)

    # Frame style
    style.configure('Custom.TFrame',
                   background=bg_color)

    # Channel button styles
    style.configure('Channel.TFrame',
                   relief='raised',
                   borderwidth=1)

    style.configure('Channel.TLabel',
                   font=('Segoe UI', 10, 'bold'),
                   foreground='#333333',
                   padding=3)

    style.configure('ChannelSmall.TLabel',
                   font=('Segoe UI', 8),
                   foreground='#666666',
                   padding=2)

def stop_search_process():
    global stop_search
    stop_search = True
    stop_button.configure(state='disabled')
    search_button.configure(state='normal', text="Ara")

def process_bulk_search():
    global stop_search
    stop_search = False
    stop_button.configure(state='normal')

    # Toplu sorgu metnini al
    bulk_queries = bulk_text.get("1.0", tk.END).strip()

    if not bulk_queries:
        messagebox.showerror("Hata", "Lütfen en az bir sorgu girin!")
        return

    # Sorguları satır satır ayır
    queries = []
    for line in bulk_queries.split('\n'):
        if '|' in line:
            url, keyword = line.split('|', 1)
            queries.append((url.strip(), keyword.strip()))

    if not queries:
        messagebox.showerror("Hata", "Geçerli sorgu bulunamadı!\nFormat: URL | Anahtar Kelime")
        return

    # Ana pencereyi göster
    notebook.select(0)  # Ana sekmeye geç

    # Her sorgu için arama yap
    for url, keyword in queries:
        if stop_search:
            messagebox.showinfo("Bilgi", "Sorgu işlemi durduruldu!")
            stop_button.configure(state='disabled')
            break

        # URL ve keyword'ü giriş alanlarına yerleştir
        url_entry.delete(0, tk.END)
        url_entry.insert(0, url)
        keyword_entry.delete(0, tk.END)
        keyword_entry.insert(0, keyword)

        # Aramayı başlat
        search_video()

        # 5 saniye bekle
        time.sleep(5)

    stop_button.configure(state='disabled')

def search_video():
    try:
        global stop_search
        driver = None

        # Arama başladığında butonu devre dışı bırak ve metni değiştir
        search_button.configure(state='disabled', text="Aranıyor...")
        root.update()

        if stop_search:
            search_button.configure(state='normal', text="Ara")
            return

        # Kullanıcının girdiği URL ve Keyword'ü al
        keyword = keyword_entry.get()
        url = url_entry.get()

        if not keyword or not url:
            messagebox.showerror("Hata", "Lütfen URL ve anahtar kelime girin!")
            search_button.configure(state='normal', text="Ara")
            return

        # Video ID'sini URL'den çıkaralım
        target_video_id = url.split("watch?v=")[-1]

        if stop_search:
            search_button.configure(state='normal', text="Ara")
            return

        # Chrome ayarlarını yapalım
        options = webdriver.ChromeOptions()
        options.add_argument('--headless=new')  # Yeni headless modu
        options.add_argument('--disable-gpu')
        options.add_argument('--incognito')  # Gizli mod
        options.add_argument('--start-maximized')
        options.add_argument('--disable-software-rasterizer')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option('excludeSwitches', ['enable-logging'])
        options.add_experimental_option('excludeSwitches', ['enable-automation'])

        # Çerez ve önbellek temizleme
        options.add_argument('--disable-application-cache')
        options.add_argument('--disable-cache')
        options.add_argument('--disable-offline-load-stale-cache')
        options.add_argument('--disk-cache-size=0')
        options.add_argument('--no-cache')
        options.add_argument('--disable-cookies')
        options.add_argument('--disable-storage')
        options.add_argument('--disable-databases')

        # Türkiye lokasyonu için dil ve bölge ayarları
        options.add_argument('--lang=tr')
        options.add_argument('--accept-lang=tr-TR,tr')

        # Kullanıcı ajanı (Türkiye'den normal bir kullanıcı gibi)
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36')

        try:
            # WebDriver başlatma
            service = Service()
            driver = webdriver.Chrome(service=service, options=options)

            # Çerezleri temizle
            driver.delete_all_cookies()

        except Exception as e:
            # WebDriver başlatılamasa bile kayıt alalım
            video_position = 0
            video_views = "0"
            video_upload_time = "Bulunamadı"

            results_text.insert(tk.END,
                f"Video sırası: {video_position}\n"
                f"Aranan kelime: {keyword}\n"
                f"Video URL: {url}\n"
                f"İzlenme sayısı: {video_views}\n"
                f"Yüklenme zamanı: {video_upload_time}\n"
                f"Hata: Chrome WebDriver başlatılamadı - {e}\n"
            )

            # Ranks tablosuna kaydet
            update_ranks_record(url, keyword, video_position, video_views, video_upload_time)

            search_button.configure(state='normal', text="Ara")
            return

        # Progress bar'ı göster ve güncelle
        progress_var.set(10)
        root.update()

        if stop_search:
            if driver:
                driver.quit()
            search_button.configure(state='normal', text="Ara")
            return

        # YouTube'a git ve dil/bölge ayarını kontrol et
        driver.get('https://www.youtube.com/?gl=TR&hl=tr')
        time.sleep(2)
        progress_var.set(20)
        root.update()

        if stop_search:
            driver.quit()
            search_button.configure(state='normal', text="Ara")
            return

        # Arama kutusunu bul
        search_box = driver.find_element(By.NAME, 'search_query')
        time.sleep(1)
        progress_var.set(30)
        root.update()

        if stop_search:
            driver.quit()
            search_button.configure(state='normal', text="Ara")
            return

        # Arama yapmak istediğimiz kelimeyi gir
        search_box.send_keys(keyword)
        time.sleep(1)
        progress_var.set(40)
        root.update()

        if stop_search:
            driver.quit()
            search_button.configure(state='normal', text="Ara")
            return

        # Arama butonuna bas
        search_box.send_keys(Keys.RETURN)
        time.sleep(2)
        progress_var.set(50)
        root.update()

        if stop_search:
            driver.quit()
            search_button.configure(state='normal', text="Ara")
            return

        # Video başlıklarını kontrol etme
        video_titles = driver.find_elements(By.XPATH, '//a[@id="video-title"]')
        print(f"Toplam {len(video_titles)} video başlığı bulundu.")

        results_text.delete(1.0, tk.END)

        video_urls = set()
        found_video = False
        result_index = 1
        video_position = 0  # Varsayılan değer 0 olarak ayarlandı
        video_views = "0"
        video_upload_time = "Bulunamadı"

        progress_var.set(60)
        root.update()

        while len(video_urls) < 100 and not stop_search:
            for index, video_title in enumerate(video_titles):
                if stop_search:
                    break

                video_href = video_title.get_attribute('href')

                if video_href is not None and 'watch?v=' in video_href:
                    if video_href not in video_urls:
                        video_urls.add(video_href)

                        if target_video_id in video_href:
                            # Sadece hedef video bulunduğunda meta verileri çek
                            views = "0"
                            upload_time = "Bulunamadı"

                            try:
                                # Video container'ı bul
                                video_container = video_title.find_element(By.XPATH, './/ancestor::ytd-video-renderer')

                                # Meta verileri bul
                                meta_items = video_container.find_elements(By.CSS_SELECTOR, 'span.inline-metadata-item')

                                if meta_items:
                                    # İzlenme sayısı (ilk span)
                                    if len(meta_items) > 0:
                                        views_text = meta_items[0].text
                                        if "görüntüleme" in views_text.lower():
                                            views = views_text.split(" görüntüleme")[0].strip()

                                    # Yüklenme zamanı (ikinci span)
                                    if len(meta_items) > 1:
                                        upload_time = meta_items[1].text

                            except Exception as e:
                                print(f"Meta veri okuma hatası: {str(e)}")

                            video_position = result_index
                            video_views = views
                            video_upload_time = upload_time
                            found_video = True
                            break
                        result_index += 1

                if len(video_urls) >= 100 or found_video:
                    break

            if len(video_urls) >= 100 or found_video:
                break

            # Scroll yap ve yeni videoları yükle
            last_height = driver.execute_script("return document.documentElement.scrollHeight")
            driver.execute_script("window.scrollTo(0, document.documentElement.scrollHeight);")
            time.sleep(2)

            # Yeni yüksekliği kontrol et
            new_height = driver.execute_script("return document.documentElement.scrollHeight")
            if new_height == last_height:  # Sayfa sonuna gelindi
                break

            # Yeni video başlıklarını al
            video_titles = driver.find_elements(By.XPATH, '//a[@id="video-title"]')

        progress_var.set(90)
        root.update()

        # Her durumda sonuçları göster ve kaydet
        results_text.insert(tk.END,
            f"Video sırası: {video_position}\n"
            f"Aranan kelime: {keyword}\n"
            f"Video URL: {url}\n"
            f"İzlenme sayısı: {video_views}\n"
            f"Yüklenme zamanı: {video_upload_time}\n"
        )

        search_history.append([
            video_position,
            keyword,
            url,
            video_views,
            video_upload_time
        ])
        update_history_table()

        # Her durumda Ranks tablosuna kaydet
        update_ranks_record(url, keyword, video_position, video_views, video_upload_time)

        if driver:
            driver.quit()

        progress_var.set(100)
        search_button.configure(state='normal', text="Ara")
        root.update()

    except Exception as e:
        # Hata durumunda bile kayıt alalım
        video_position = 0
        video_views = "0"
        video_upload_time = "Bulunamadı"

        messagebox.showerror("Hata", f"Bir hata oluştu: {e}")
        results_text.insert(tk.END,
            f"Video sırası: {video_position}\n"
            f"Aranan kelime: {keyword}\n"
            f"Video URL: {url}\n"
            f"İzlenme sayısı: {video_views}\n"
            f"Yüklenme zamanı: {video_upload_time}\n"
            f"Hata: {e}\n"
        )

        # Hata durumunda da Ranks tablosuna kaydet
        update_ranks_record(url, keyword, video_position, video_views, video_upload_time)

        search_button.configure(state='normal', text="Ara")
        progress_var.set(0)
        if driver:
            driver.quit()

def update_history_table():
    history_tree.delete(*history_tree.get_children())
    for record in search_history:
        history_tree.insert('', 'end', values=record)

# Ranks tablosu güncelleme fonksiyonu
def update_ranks_table():
    try:
        # Mevcut tabloyu temizle
        ranks_tree.delete(*ranks_tree.get_children())

        # Verileri yükle
        ranks_data = load_ranks_data()
        all_data = []

        # Dictionary'den listeye çevir
        for url, data in ranks_data.items():
            row_data = [
                "❌",  # Silme butonu
                data.get('URL', ''),
                data.get('Keyword', ''),
                data.get('Tag', ''),
                data.get('Channel', ''),
                data.get('Current Rank', ''),
                data.get('Previous Rank', ''),
                data.get('Current Views', ''),
                data.get('Previous Views', ''),
                data.get('Views Difference', ''),
                data.get('Upload Time', ''),
                data.get('Last Update', ''),
                data.get('Rank Change', '')
            ]
            all_data.append(row_data)

        # Sıralama kriterine göre sırala
        sort_key = sort_var.get()
        if sort_key == "rank":
            all_data.sort(key=lambda x: int(x[5]) if str(x[5]).isdigit() else 999)  # Current Rank sütunu
        elif sort_key == "views":
            all_data.sort(key=lambda x: int(x[9]) if str(x[9]).isdigit() else 0, reverse=True)  # Views Difference sütunu
        elif sort_key == "update":
            all_data.sort(key=lambda x: x[11] if x[11] else '', reverse=True)  # Last Update sütunu

        # URL, Keyword, Tag ve Channel filtresini uygula
        filter_text = tag_filter.get().lower()
        if filter_text:
            all_data = [row for row in all_data if
                       filter_text in str(row[1]).lower() or  # URL sütunu
                       filter_text in str(row[2]).lower() or  # Keyword sütunu
                       filter_text in str(row[3]).lower() or  # Tag sütunu
                       filter_text in str(row[4]).lower()]    # Channel sütunu

        # Renk tag'lerini oluştur
        ranks_tree.tag_configure('up_arrow', foreground='#00ff00')  # Yeşil
        ranks_tree.tag_configure('down_arrow', foreground='#ff0000')  # Kırmızı
        ranks_tree.tag_configure('equal_sign', foreground='black')  # Siyah
        ranks_tree.tag_configure('delete_button', foreground='red')  # Silme butonu kırmızı

        # Verileri tabloya ekle
        for row in all_data:
            item = ranks_tree.insert('', 'end', values=row)

            # Silme butonu için kırmızı renk
            if row[0] == "❌":
                ranks_tree.item(item, tags=('delete_button',))

            # Rank Change sütunu için renk
            rank_change = row[12]  # Rank Change sütunu
            if rank_change == "↑":
                ranks_tree.item(item, tags=('up_arrow',))
            elif rank_change == "↓":
                ranks_tree.item(item, tags=('down_arrow',))
            elif rank_change == "=":
                ranks_tree.item(item, tags=('equal_sign',))

    except Exception as e:
        print(f"Ranks tablosu güncelleme hatası: {e}")

# Notebook (Sekmeli arayüz) oluştur
notebook = ttk.Notebook(root)
notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

# Ana sekme frame'i
main_frame = ttk.Frame(notebook, style='Custom.TFrame')
notebook.add(main_frame, text='Ana Sayfa')

# Sıralama değişkeni
sort_var = tk.StringVar(value="rank")

# Toplu Sorgu sekmesi
bulk_frame = ttk.Frame(notebook, style='Custom.TFrame')
notebook.add(bulk_frame, text='Toplu Sorgu')

# Ranks sekmesi
ranks_frame = ttk.Frame(notebook, style='Custom.TFrame')
notebook.add(ranks_frame, text='Ranks')

# Kanallar sekmesi
channels_frame = ttk.Frame(notebook, style='Custom.TFrame')
notebook.add(channels_frame, text='Kanallar')

# Güncel sekmesi
guncel_frame = ttk.Frame(notebook, style='Custom.TFrame')
notebook.add(guncel_frame, text='Güncel')

# Ayarlar sekmesi
ayarlar_frame = ttk.Frame(notebook, style='Custom.TFrame')
notebook.add(ayarlar_frame, text='Ayarlar')

# Kanallar sekmesi için üst frame (kanal URL girişi ve butonlar)
channels_top_frame = ttk.Frame(channels_frame, style='Custom.TFrame')
channels_top_frame.pack(fill=tk.X, padx=10, pady=5)

# Kanal URL girişi
channel_url_entry = ttk.Entry(channels_top_frame, style='Custom.TEntry', width=35)
channel_url_entry.pack(side=tk.LEFT, padx=5)
channel_url_entry.insert(0, "Kanal URL'sini girin")

# Mail girişi
channel_mail_entry = ttk.Entry(channels_top_frame, style='Custom.TEntry', width=25)
channel_mail_entry.pack(side=tk.LEFT, padx=5)
channel_mail_entry.insert(0, "Mail adresini girin")

# Placeholder text için focus olayları
def on_url_entry_focus_in(event):
    if channel_url_entry.get() == "Kanal URL'sini girin":
        channel_url_entry.delete(0, tk.END)

def on_url_entry_focus_out(event):
    if not channel_url_entry.get().strip():
        channel_url_entry.insert(0, "Kanal URL'sini girin")

def on_mail_entry_focus_in(event):
    if channel_mail_entry.get() == "Mail adresini girin":
        channel_mail_entry.delete(0, tk.END)

def on_mail_entry_focus_out(event):
    if not channel_mail_entry.get().strip():
        channel_mail_entry.insert(0, "Mail adresini girin")

channel_url_entry.bind('<FocusIn>', on_url_entry_focus_in)
channel_url_entry.bind('<FocusOut>', on_url_entry_focus_out)
channel_mail_entry.bind('<FocusIn>', on_mail_entry_focus_in)
channel_mail_entry.bind('<FocusOut>', on_mail_entry_focus_out)

# Kanal ekle butonu
add_channel_button = ttk.Button(
    channels_top_frame,
    text="Kanal Ekle",
    style='Custom.TButton',
    command=lambda: add_channel()
)
add_channel_button.pack(side=tk.LEFT, padx=5)

# Check butonu
check_button = ttk.Button(
    channels_top_frame,
    text="Check",
    style='Custom.TButton',
    width=10,
    command=lambda: check_video_ranks()
)
check_button.pack(side=tk.LEFT, padx=5)

# Kanal silme butonu
delete_channel_button = ttk.Button(
    channels_top_frame,
    text="Kanal Sil",
    style='Custom.TButton',
    width=10,
    command=lambda: delete_selected_channel()
)
delete_channel_button.pack(side=tk.LEFT, padx=5)

# Kanal listesi için frame (üstte)
channels_list_frame = ttk.Frame(channels_frame, style='Custom.TFrame')
channels_list_frame.pack(fill=tk.X, padx=10, pady=5)

# Kanal butonları için canvas ve scrollbar
channels_canvas = tk.Canvas(channels_list_frame, height=60, background='#f5f5f5')
channels_canvas.pack(fill=tk.X, expand=True)

channels_scrollbar = ttk.Scrollbar(channels_list_frame, orient=tk.HORIZONTAL, command=channels_canvas.xview)
channels_scrollbar.pack(fill=tk.X)

channels_buttons_frame = ttk.Frame(channels_canvas, style='Custom.TFrame')
channels_canvas.create_window((0, 0), window=channels_buttons_frame, anchor='nw')

# Video listesi için frame (altta)
videos_frame = ttk.Frame(channels_frame, style='Custom.TFrame')
videos_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

# Video listesi için Treeview
videos_tree = ttk.Treeview(videos_frame, columns=(
    'Select', 'Thumbnail', 'URL', 'Keyword', 'Başlık', 'Rank', 'Like', 'Yorum', 'İzlenme', 'Yüklenme Tarihi', 'Günlük Değişim'
), show='headings')

# Sütun başlıkları ve genişlikleri
videos_tree.heading('Select', text='')
videos_tree.heading('Thumbnail', text='')
videos_tree.heading('URL', text='Video URL')
videos_tree.heading('Keyword', text='Keyword')
videos_tree.heading('Başlık', text='Başlık')
videos_tree.heading('Rank', text='Rank')
videos_tree.heading('Like', text='Like')
videos_tree.heading('Yorum', text='Yorum')
videos_tree.heading('İzlenme', text='İzlenme')
videos_tree.heading('Yüklenme Tarihi', text='Yüklenme Tarihi')
videos_tree.heading('Günlük Değişim', text='Günlük Değişim')

videos_tree.column('Select', width=30, anchor='center')
videos_tree.column('Thumbnail', width=100, anchor='center')
videos_tree.column('URL', width=200)
videos_tree.column('Keyword', width=150)
videos_tree.column('Başlık', width=300)
videos_tree.column('Rank', width=80, anchor='center')
videos_tree.column('Like', width=100, anchor='center')
videos_tree.column('Yorum', width=100, anchor='center')
videos_tree.column('İzlenme', width=100, anchor='center')
videos_tree.column('Yüklenme Tarihi', width=150, anchor='center')
videos_tree.column('Günlük Değişim', width=100, anchor='center')

# Seçim kutucuğu için tag oluştur
videos_tree.tag_configure('checked')
videos_tree.tag_configure('unchecked')

# Scrollbar'lar
videos_vsb = ttk.Scrollbar(videos_frame, orient="vertical", command=videos_tree.yview)
videos_hsb = ttk.Scrollbar(videos_frame, orient="horizontal", command=videos_tree.xview)
videos_tree.configure(yscrollcommand=videos_vsb.set, xscrollcommand=videos_hsb.set)

# Treeview ve scrollbar'ları yerleştir
videos_vsb.pack(side='right', fill='y')
videos_hsb.pack(side='bottom', fill='x')
videos_tree.pack(fill='both', expand=True)

# Video silme butonu ve fonksiyonu
def delete_selected_videos():
    # Seçili videoları bul
    selected_items = [item for item in videos_tree.get_children()
                     if 'checked' in videos_tree.item(item, 'tags')]

    if not selected_items:
        messagebox.showinfo("Bilgi", "Lütfen silmek istediğiniz videoları seçin!")
        return

    # Onay sor
    if not messagebox.askyesno("Silme Onayı", f"{len(selected_items)} videoyu silmek istediğinizden emin misiniz?"):
        return

    try:
        # Mevcut kanal adını bul
        current_channel = None
        # Aktif sekmeyi kontrol et
        selected_tab = notebook.select()
        tab_text = notebook.tab(selected_tab, "text")

        if tab_text == "Kanallar":
            # İlk seçili videonun URL'sinden kanal adını bulmaya çalış
            for item in selected_items:
                url = videos_tree.item(item)['values'][2]  # URL 3. sütunda (index 2)
                if url and 'youtube.com' in url:
                    # URL'den video ID'sini çıkar
                    video_id = url.split('watch?v=')[-1].split('&')[0]

                    # Tüm kanal verilerini kontrol et
                    all_videos_data = load_channel_videos_data()
                    for ch_name, videos in all_videos_data.items():
                        for video in videos:
                            if video_id in video['url']:
                                current_channel = ch_name
                                break
                        if current_channel:
                            break
                    if current_channel:
                        break

        if not current_channel:
            messagebox.showerror("Hata", "Kanal bilgisi bulunamadı!")
            return

        # JSON verilerini yükle
        all_videos_data = load_channel_videos_data()
        if current_channel not in all_videos_data:
            messagebox.showerror("Hata", "Kanal verileri bulunamadı!")
            return

        channel_videos = all_videos_data[current_channel]

        # Silinecek URL'leri topla
        urls_to_delete = [videos_tree.item(item)['values'][2] for item in selected_items]

        # Videoları JSON'dan sil
        new_videos = [video for video in channel_videos if video['url'] not in urls_to_delete]
        all_videos_data[current_channel] = new_videos

        # JSON'a kaydet
        with open(CHANNEL_VIDEOS_FILE, 'w', encoding='utf-8') as f:
            json.dump(all_videos_data, f, ensure_ascii=False, indent=4)

        # Treeview'dan sil
        for item in selected_items:
            videos_tree.delete(item)

        messagebox.showinfo("Bilgi", f"{len(selected_items)} video başarıyla silindi!")

    except Exception as e:
        messagebox.showerror("Hata", f"Video silme işlemi sırasında bir hata oluştu: {e}")

# Silme butonu frame'i
delete_frame = ttk.Frame(videos_frame, style='Custom.TFrame')
delete_frame.pack(fill=tk.X, pady=5)

# Silme butonu
delete_button = ttk.Button(
    delete_frame,
    text="Seçili Videoları Sil",
    style='Custom.TButton',
    command=delete_selected_videos
)
delete_button.pack(side=tk.RIGHT, padx=5)

def load_channels_data():
    try:
        if os.path.exists(CHANNELS_DATA_FILE):
            with open(CHANNELS_DATA_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    except Exception as e:
        print(f"Kanal verisi yükleme hatası: {e}")
        return {}

def save_channels_data(data):
    try:
        with open(CHANNELS_DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
    except Exception as e:
        print(f"Kanal verisi kaydetme hatası: {e}")

def create_channel_button(url, channel_name, mail=""):
    # Frame oluştur (dikey düzen için)
    button_frame = ttk.Frame(channels_buttons_frame, style='Channel.TFrame')
    button_frame.pack(side=tk.LEFT, padx=4, pady=4)

    # Kanal adı etiketi
    channel_label = ttk.Label(button_frame, text=channel_name, style='Channel.TLabel')
    channel_label.pack(pady=(5,2))

    # Mail etiketi
    mail_label = ttk.Label(button_frame, text=mail, style='ChannelSmall.TLabel')
    mail_label.pack(pady=(0,5))

    # Frame'e tıklama olaylarını bağla
    button_frame.bind('<Button-1>', lambda e: load_channel_videos(url))
    channel_label.bind('<Button-1>', lambda e: load_channel_videos(url))
    mail_label.bind('<Button-1>', lambda e: load_channel_videos(url))
    channel_label.bind('<Double-Button-1>', lambda e: edit_channel_mail(url))
    mail_label.bind('<Double-Button-1>', lambda e: edit_channel_mail(url))

def edit_channel_mail(url):
    # Düzenleme penceresi oluştur
    edit_window = tk.Toplevel()
    edit_window.title("Mail Düzenle")
    edit_window.geometry("300x150")

    # Pencereyi merkeze al
    edit_window.transient(root)
    edit_window.grab_set()

    # Mail giriş alanı
    mail_label = ttk.Label(edit_window, text="Mail Adresi:", style='Custom.TLabel')
    mail_label.pack(pady=10)

    mail_entry = ttk.Entry(edit_window, style='Custom.TEntry')
    mail_entry.pack(pady=5)

    # Mevcut mail varsa göster
    channels_data = load_channels_data()
    if url in channels_data and 'mail' in channels_data[url]:
        mail_entry.insert(0, channels_data[url]['mail'])

    def save_mail():
        mail = mail_entry.get().strip()
        if mail:
            # Kanal verilerini güncelle
            channels_data = load_channels_data()
            if url in channels_data:
                channels_data[url]['mail'] = mail
                save_channels_data(channels_data)

                # Butonları yeniden yükle
                refresh_channel_buttons()

            edit_window.destroy()

    # Kaydet butonu
    save_button = ttk.Button(edit_window, text="Kaydet", style='Custom.TButton', command=save_mail)
    save_button.pack(pady=20)

def refresh_channel_buttons():
    # Mevcut butonları temizle
    for widget in channels_buttons_frame.winfo_children():
        widget.destroy()

    # Butonları yeniden oluştur
    channels_data = load_channels_data()
    for url, data in channels_data.items():
        channel_name = data.get('name', '')
        mail = data.get('mail', '')
        if channel_name:
            create_channel_button(url, channel_name, mail)

    # Canvas'ı güncelle
    channels_buttons_frame.update_idletasks()
    channels_canvas.configure(scrollregion=channels_canvas.bbox('all'))

def add_channel():
    url = channel_url_entry.get().strip()
    mail = channel_mail_entry.get().strip()

    # Placeholder text kontrolü
    if not url or url == "Kanal URL'sini girin":
        messagebox.showwarning("Uyarı", "Lütfen geçerli bir kanal URL'si girin!")
        return

    # Mail placeholder kontrolü
    if mail == "Mail adresini girin":
        mail = ""

    # URL formatı kontrolü
    if not ('youtube.com' in url and ('@' in url or '/c/' in url or '/channel/' in url or '/user/' in url)):
        messagebox.showerror("Hata", "Geçerli bir YouTube kanal URL'si girin!\nÖrnek: https://www.youtube.com/@kanaladi")
        return

    try:
        # Duplicate kanal kontrolü
        channels_data = load_channels_data()
        if url in channels_data:
            messagebox.showinfo("Bilgi", "Bu kanal zaten eklenmiş!")
            return

        # Loading göstergesi
        add_channel_button.configure(state='disabled', text="Ekleniyor...")
        root.update()

        # Kanal adını URL'den al (geliştirilmiş)
        if '@' in url:
            channel_name = url.split('@')[1].split('/')[0]
        elif '/c/' in url:
            channel_name = url.split('/c/')[1].split('/')[0]
        elif '/channel/' in url:
            channel_name = url.split('/channel/')[1].split('/')[0]
        elif '/user/' in url:
            channel_name = url.split('/user/')[1].split('/')[0]
        else:
            channel_name = url.split('/')[-1] if url.split('/')[-1] else "Bilinmeyen Kanal"

        # Kanal verilerini kaydet
        channels_data[url] = {
            'name': channel_name,
            'mail': mail,  # Girilen mail adresini kaydet
            'added_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        save_channels_data(channels_data)

        # Kanal butonunu oluştur
        create_channel_button(url, channel_name, mail)

        # Canvas'ı güncelle
        channels_buttons_frame.update_idletasks()
        channels_canvas.configure(scrollregion=channels_canvas.bbox('all'))

        # Girişleri temizle ve placeholder'ları geri koy
        channel_url_entry.delete(0, tk.END)
        channel_url_entry.insert(0, "Kanal URL'sini girin")
        channel_mail_entry.delete(0, tk.END)
        channel_mail_entry.insert(0, "Mail adresini girin")

        # Videoları yükle (hata durumunda kanal silinmesin)
        try:
            load_channel_videos(url)
            messagebox.showinfo("Başarılı", f"'{channel_name}' kanalı başarıyla eklendi!")
        except Exception as video_error:
            print(f"Video yükleme hatası: {video_error}")
            messagebox.showwarning("Uyarı", f"Kanal eklendi ancak videolar yüklenemedi.\nHata: {video_error}")

    except Exception as e:
        messagebox.showerror("Hata", f"Kanal eklenirken bir hata oluştu: {e}")
    finally:
        # Butonu normale döndür
        add_channel_button.configure(state='normal', text="Kanal Ekle")

def delete_selected_channel():
    """Seçili kanalı siler"""
    try:
        # Şu anda görüntülenen kanalı bul
        current_channel_url = None
        current_channel_name = None

        # Video listesinde video varsa, ilk videonun URL'sinden kanal bilgisini al
        if videos_tree.get_children():
            first_item = videos_tree.get_children()[0]
            first_video_url = videos_tree.item(first_item)['values'][2]  # URL 3. sütunda

            # Kanal verilerini yükle
            channels_data = load_channels_data()
            channel_videos_data = load_channel_videos_data()

            # Hangi kanala ait olduğunu bul
            for ch_name, videos in channel_videos_data.items():
                for video in videos:
                    if video['url'] == first_video_url:
                        # Kanal URL'sini bul
                        for url, data in channels_data.items():
                            if data.get('name') == ch_name:
                                current_channel_url = url
                                current_channel_name = ch_name
                                break
                        break
                if current_channel_url:
                    break

        if not current_channel_url:
            messagebox.showwarning("Uyarı", "Silmek için önce bir kanal seçin!")
            return

        # Onay sor
        if not messagebox.askyesno("Silme Onayı",
                                  f"'{current_channel_name}' kanalını silmek istediğinizden emin misiniz?\n\n"
                                  f"Bu işlem:\n"
                                  f"• Kanal bilgilerini\n"
                                  f"• Tüm video verilerini\n"
                                  f"• Kanal butonunu\n"
                                  f"silecektir."):
            return

        # Kanal verilerini sil
        channels_data = load_channels_data()
        if current_channel_url in channels_data:
            del channels_data[current_channel_url]
            save_channels_data(channels_data)

        # Video verilerini sil
        channel_videos_data = load_channel_videos_data()
        if current_channel_name in channel_videos_data:
            del channel_videos_data[current_channel_name]
            with open(CHANNEL_VIDEOS_FILE, 'w', encoding='utf-8') as f:
                json.dump(channel_videos_data, f, ensure_ascii=False, indent=4)

        # Video listesini temizle
        videos_tree.delete(*videos_tree.get_children())

        # Kanal butonlarını yenile
        refresh_channel_buttons()

        messagebox.showinfo("Başarılı", f"'{current_channel_name}' kanalı başarıyla silindi!")

    except Exception as e:
        messagebox.showerror("Hata", f"Kanal silme işlemi sırasında bir hata oluştu: {e}")

def load_saved_channels():
    try:
        channels_data = load_channels_data()
        for url, data in channels_data.items():
            channel_name = data.get('name', '')
            mail = data.get('mail', '')
            if channel_name:
                create_channel_button(url, channel_name, mail)

        # Canvas'ı güncelle
        channels_buttons_frame.update_idletasks()
        channels_canvas.configure(scrollregion=channels_canvas.bbox('all'))
    except Exception as e:
        print(f"Kayıtlı kanalları yükleme hatası: {e}")

def check_video_ranks():
    try:
        # Ranks verilerini yükle
        ranks_data = load_ranks_data()
        # Kanal videolarını yükle
        channel_videos_data = load_channel_videos_data()

        # Güncellenecek kanal adını bul
        current_channel = None
        selected_tab = notebook.select()
        tab_text = notebook.tab(selected_tab, "text")

        if tab_text == "Kanallar" and videos_tree.get_children():
            # İlk videonun URL'sinden kanal adını bulmaya çalış
            first_item = videos_tree.get_children()[0]
            first_url = videos_tree.item(first_item)['values'][2]  # URL 3. sütunda (index 2)

            # Kanal adını bul
            for ch_name, videos in channel_videos_data.items():
                for video in videos:
                    if video['url'] == first_url:
                        current_channel = ch_name
                        break
                if current_channel:
                    break

        # Treeview'daki tüm videoları kontrol et
        for item in videos_tree.get_children():
            # Video URL'sini al
            video_url = videos_tree.item(item)['values'][2]  # URL 3. sütunda (index 2)

            # Ranks verilerinde bu URL'yi ara
            if video_url in ranks_data:
                # Rank ve Keyword değerlerini al
                current_rank = ranks_data[video_url].get('Current Rank', '-')
                keyword = ranks_data[video_url].get('Keyword', '-')

                # Mevcut değerleri al
                values = list(videos_tree.item(item)['values'])

                # Keyword ve Rank değerlerini güncelle
                values[3] = keyword  # Keyword 4. sütunda (index 3)
                values[5] = current_rank  # Rank 6. sütunda (index 5)

                # Treeview'ı güncelle
                videos_tree.item(item, values=values)

                # JSON'da da güncelle
                if current_channel:
                    for video in channel_videos_data.get(current_channel, []):
                        if video['url'] == video_url:
                            video['keyword'] = keyword
                            video['rank'] = current_rank
                            break
            else:
                # URL ranks verilerinde yoksa, rank ve keyword değerlerini '-' yap
                values = list(videos_tree.item(item)['values'])
                values[3] = '-'  # Keyword
                values[5] = '-'  # Rank
                videos_tree.item(item, values=values)

        # Değişiklikleri JSON'a kaydet
        if current_channel and current_channel in channel_videos_data:
            with open(CHANNEL_VIDEOS_FILE, 'w', encoding='utf-8') as f:
                json.dump(channel_videos_data, f, ensure_ascii=False, indent=4)
            print(f"Video bilgileri güncellendi ve kaydedildi.")

    except Exception as e:
        print(f"Rank kontrolü hatası: {e}")

def load_channel_videos(channel_url):
    try:
        # Önce kayıtlı videoları kontrol et
        channel_name = channel_url.split('@')[1].split('/')[0] if '@' in channel_url else channel_url.split('/')[-1]
        saved_data = load_channel_videos_data()

        if channel_name in saved_data:
            # Kayıtlı videoları yükle
            videos_tree.delete(*videos_tree.get_children())
            for video_data in saved_data[channel_name]:
                item = videos_tree.insert('', 'end', values=(
                    CHECK_OFF,  # Seçim kutucuğu için boş değer
                    video_data['thumbnail'],
                    video_data['url'],
                    video_data.get('keyword', '-'),  # Keyword değeri
                    video_data['title'],
                    video_data.get('rank', '-'),  # Rank değeri
                    video_data['likes'],
                    video_data['comments'],
                    video_data['views'],
                    video_data['upload_time'],
                    video_data['daily_change']
                ))
                videos_tree.item(item, tags=('unchecked',))
            print(f"Kayıtlı {len(saved_data[channel_name])} video yüklendi.")
            return

        # Chrome ayarları
        options = webdriver.ChromeOptions()
        options.add_argument('--headless=new')
        options.add_argument('--disable-gpu')
        options.add_argument('--incognito')

        # WebDriver başlat
        service = Service()
        driver = webdriver.Chrome(service=service, options=options)

        # Video listesini temizle
        videos_tree.delete(*videos_tree.get_children())

        # Kanala git - videos sayfasına direkt git
        if not channel_url.endswith('/videos'):
            if channel_url.endswith('/'):
                channel_url = channel_url + 'videos'
            else:
                channel_url = channel_url + '/videos'

        print(f"Kanal videoları yükleniyor: {channel_url}")
        driver.get(channel_url)
        time.sleep(5)  # Daha uzun bekleme süresi

        # Videoları saklamak için liste
        videos_data = []

        # Scroll işlemi için sayaç
        scroll_count = 0
        max_scrolls = 20

        while scroll_count < max_scrolls:
            # Farklı video seçicilerini dene
            video_selectors = [
                'ytd-rich-item-renderer',
                'ytd-grid-video-renderer',
                'ytd-video-renderer',
                'div#contents ytd-rich-item-renderer'
            ]

            videos = []
            for selector in video_selectors:
                videos = driver.find_elements(By.CSS_SELECTOR, selector)
                if videos:
                    print(f"'{selector}' ile {len(videos)} video bulundu")
                    break

            if not videos:
                print("Hiç video bulunamadı, farklı yöntem deneniyor...")
                # JavaScript ile video linklerini bul
                video_links = driver.execute_script("""
                    return Array.from(document.querySelectorAll('a[href*="/watch?v="]')).map(a => ({
                        href: a.href,
                        title: a.title || a.textContent.trim()
                    }));
                """)

                if video_links:
                    print(f"JavaScript ile {len(video_links)} video linki bulundu")
                    # Bu linkleri işle
                    for link_data in video_links[:20]:  # İlk 20 video
                        try:
                            video_url = link_data['href']
                            title = link_data['title']

                            if not video_url or 'watch?v=' not in video_url:
                                continue

                            # URL'yi temizle
                            if '&' in video_url:
                                video_url = video_url.split('&')[0]

                            video_data = {
                                'thumbnail': '',
                                'url': video_url,
                                'title': title,
                                'likes': "0",
                                'comments': "0",
                                'views': "0",
                                'upload_time': "",
                                'daily_change': "0",
                                'last_update': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            }

                            videos_data.append(video_data)

                            # Treeview'a ekle
                            item = videos_tree.insert('', 'end', values=(
                                CHECK_OFF,
                                video_data['thumbnail'],
                                video_data['url'],
                                video_data.get('keyword', '-'),
                                video_data['title'],
                                video_data.get('rank', '-'),
                                video_data['likes'],
                                video_data['comments'],
                                video_data['views'],
                                video_data['upload_time'],
                                video_data['daily_change']
                            ))
                            videos_tree.item(item, tags=('unchecked',))

                        except Exception as e:
                            print(f"JavaScript video işleme hatası: {e}")
                            continue

                    # JavaScript yöntemi başarılıysa döngüden çık
                    if videos_data:
                        break

            for video in videos:
                try:
                    title_element = video.find_element(By.CSS_SELECTOR, '#video-title-link')
                    video_url = title_element.get_attribute('href')
                    title = title_element.get_attribute('title')

                    if not video_url:
                        continue

                    # URL'deki & işaretinden sonraki kısmı temizle
                    if '&' in video_url:
                        video_url = video_url.split('&')[0]

                    thumbnail = video.find_element(By.CSS_SELECTOR, 'yt-image img').get_attribute('src')

                    # İzlenme ve yüklenme zamanı için yeni seçici
                    meta_items = video.find_elements(By.CSS_SELECTOR, 'span.inline-metadata-item')
                    views = meta_items[0].text if len(meta_items) > 0 else "0"
                    upload_time = meta_items[1].text if len(meta_items) > 1 else ""

                    # Video verilerini hazırla
                    video_data = {
                        'thumbnail': thumbnail,
                        'url': video_url.split('&')[0] if '&' in video_url else video_url,  # URL'yi temizle
                        'title': title,
                        'likes': "0",
                        'comments': "0",
                        'views': views,
                        'upload_time': upload_time,
                        'daily_change': "0",
                        'last_update': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }

                    # URL'yi http ile başlatmıyorsa ekle
                    if not video_data['url'].startswith('http'):
                        video_data['url'] = f"https://www.youtube.com{video_data['url']}"

                    # Listeye ekle
                    videos_data.append(video_data)

                    # Treeview'a ekle
                    item = videos_tree.insert('', 'end', values=(
                        CHECK_OFF,  # Seçim kutucuğu için boş değer
                        video_data['thumbnail'],
                        video_data['url'],
                        video_data.get('keyword', '-'),  # Keyword değeri
                        video_data['title'],
                        video_data.get('rank', '-'),  # Rank değeri
                        video_data['likes'],
                        video_data['comments'],
                        video_data['views'],
                        video_data['upload_time'],
                        video_data['daily_change']
                    ))
                    videos_tree.item(item, tags=('unchecked',))

                except Exception as e:
                    print(f"Video bilgisi alma hatası: {e}")
                    continue

            # Scroll yap
            last_height = driver.execute_script("return document.documentElement.scrollHeight")
            driver.execute_script("window.scrollTo(0, document.documentElement.scrollHeight);")
            time.sleep(2)

            new_height = driver.execute_script("return document.documentElement.scrollHeight")
            if new_height == last_height:
                break

            scroll_count += 1

        # Verileri kaydet
        save_channel_videos_data(channel_name, videos_data)

    except Exception as e:
        print(f"Video listesi yükleme hatası: {e}")
        messagebox.showerror("Hata", f"Videolar yüklenirken bir hata oluştu: {e}")
    finally:
        if 'driver' in locals():
            driver.quit()

# Uygulama başladığında kayıtlı kanalları yükle
load_saved_channels()

# Canvas scroll olayları
def on_canvas_configure(event):
    channels_canvas.configure(scrollregion=channels_canvas.bbox('all'))

channels_buttons_frame.bind('<Configure>', on_canvas_configure)
channels_canvas.configure(xscrollcommand=channels_scrollbar.set)

# Video seçim ve URL kopyalama için binding
def toggle_video_selection(event):
    item = videos_tree.identify_row(event.y)
    column = videos_tree.identify_column(event.x)

    if item and column == '#1':  # Seçim kutucuğu sütunu
        current_tags = videos_tree.item(item, 'tags')
        values = list(videos_tree.item(item)['values'])

        if 'checked' in current_tags:
            videos_tree.item(item, tags=('unchecked',))
            values[0] = CHECK_OFF
        else:
            videos_tree.item(item, tags=('checked',))
            values[0] = CHECK_ON

        videos_tree.item(item, values=values)
    elif item and column == '#3':  # URL sütunu
        # URL'yi panoya kopyala
        url = videos_tree.item(item)['values'][2]  # URL 3. sütunda (index 2)
        if url:
            root.clipboard_clear()
            root.clipboard_append(url)
            messagebox.showinfo("Bilgi", "Video URL'si panoya kopyalandı!")

videos_tree.bind('<Button-1>', toggle_video_selection)

# Filtreleme araçları
filter_frame = ttk.LabelFrame(ranks_frame, text="Filtreleme ve Sıralama", style='Custom.TFrame')
filter_frame.pack(fill=tk.X, padx=5, pady=5)

# Sıralama seçenekleri
sort_label = ttk.Label(filter_frame, text="Sırala:", style='Custom.TLabel')
sort_label.pack(side=tk.LEFT, padx=5)

sort_options = [
    ("Rank", "rank"),
    ("İzlenme Artışı", "views"),
    ("Son Güncelleme", "update")
]

for text, value in sort_options:
    ttk.Radiobutton(filter_frame, text=text, value=value, variable=sort_var, style='Custom.TRadiobutton').pack(side=tk.LEFT, padx=5)

# Tag filtresi
tag_label = ttk.Label(filter_frame, text="Tag Filtresi:", style='Custom.TLabel')
tag_label.pack(side=tk.LEFT, padx=(20, 5))
tag_filter = ttk.Entry(filter_frame, style='Custom.TEntry', width=20)
tag_filter.pack(side=tk.LEFT, padx=5)

# TARA butonu
scan_button = ttk.Button(
    filter_frame,
    text="TARA",
    style='Custom.TButton',
    command=lambda: scan_all_urls_and_keywords()
)
scan_button.pack(side=tk.RIGHT, padx=20)

# Toplu Sorgu sekmesi içeriği
bulk_label = ttk.Label(
    bulk_frame,
    text="Sorguları alt alta girin (Format: URL | Anahtar Kelime)",
    style='Custom.TLabel',
    font=('Segoe UI', 12)
)
bulk_label.pack(pady=10)

bulk_text = tk.Text(bulk_frame, height=20, width=80, font=('Segoe UI', 10))
bulk_text.pack(pady=10, padx=20)

bulk_button = ttk.Button(
    bulk_frame,
    text="Toplu Tara",
    style='Custom.TButton',
    command=process_bulk_search
)
bulk_button.pack(pady=10)

# Örnek format
example_label = ttk.Label(
    bulk_frame,
    text="Örnek:\nhttps://youtube.com/watch?v=123 | anahtar kelime 1\nhttps://youtube.com/watch?v=456 | anahtar kelime 2",
    style='Custom.TLabel',
    justify=tk.LEFT
)
example_label.pack(pady=10)

# Ana sayfa içeriği
title_label = ttk.Label(
    main_frame,
    text="YouTube Video Sıralama Analizi",
    style='Custom.TLabel',
    font=('Segoe UI', 24, 'bold')
)
title_label.pack(pady=(0, 20))

# Giriş alanları frame
input_frame = ttk.Frame(main_frame, style='Custom.TFrame')
input_frame.pack(fill=tk.X, padx=20)

# URL giriş alanı
url_label = ttk.Label(input_frame, text="YouTube Video URL:", style='Custom.TLabel')
url_label.pack(anchor='w')
url_entry = ttk.Entry(input_frame, style='Custom.TEntry', width=70)
url_entry.pack(fill=tk.X, pady=(0, 10))

# Keyword giriş alanı
keyword_label = ttk.Label(input_frame, text="Anahtar Kelime:", style='Custom.TLabel')
keyword_label.pack(anchor='w')
keyword_entry = ttk.Entry(input_frame, style='Custom.TEntry', width=70)
keyword_entry.pack(fill=tk.X, pady=(0, 20))

# Progress bar
progress_var = tk.DoubleVar()
progress_bar = ttk.Progressbar(
    input_frame,
    variable=progress_var,
    maximum=100,
    mode='determinate',
    length=300
)
progress_bar.pack(pady=(0, 10))

# Arama butonu
search_button = ttk.Button(
    input_frame,
    text="Ara",
    style='Custom.TButton',
    command=search_video
)
search_button.pack(pady=(0, 20))

# Sonuçlar frame
results_frame = ttk.LabelFrame(main_frame, text="Sonuçlar", style='Custom.TFrame')
results_frame.pack(fill=tk.X, padx=20, pady=(0, 20))

results_text = tk.Text(results_frame, height=4, font=('Segoe UI', 10))
results_text.pack(fill=tk.X, padx=5, pady=5)

# Geçmiş frame
history_frame = ttk.LabelFrame(main_frame, text="Geçmiş Aramalar", style='Custom.TFrame')
history_frame.pack(fill=tk.BOTH, expand=True, padx=20)

# Treeview için columns
columns = ('Sıra', 'Anahtar Kelime', 'URL', 'İzlenme Sayısı', 'Yüklenme Zamanı')
history_tree = ttk.Treeview(history_frame, columns=columns, show='headings')

# Column headings
for col in columns:
    history_tree.heading(col, text=col)
    if col == 'URL':
        history_tree.column(col, width=300)
    elif col in ('İzlenme Sayısı', 'Yüklenme Zamanı'):
        history_tree.column(col, width=150)
    else:
        history_tree.column(col, width=100)

history_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

# Scrollbar ekle
scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=history_tree.yview)
scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
history_tree.configure(yscrollcommand=scrollbar.set)

# Alt frame (stop butonu için)
bottom_frame = ttk.Frame(main_frame, style='Custom.TFrame')
bottom_frame.pack(fill=tk.X, padx=20, pady=10)

# Stop butonu
stop_button = ttk.Button(
    bottom_frame,
    text="Durdur",
    style='Custom.TButton',
    command=stop_search_process,
    state='disabled'
)
stop_button.pack(side=tk.RIGHT, padx=5)

# Custom style oluştur
create_custom_style()

# Treeview ve scrollbar'ları oluştur
ranks_tree_frame = ttk.Frame(ranks_frame)
ranks_tree_frame.pack(fill='both', expand=True, padx=5, pady=5)

ranks_tree_scroll_y = ttk.Scrollbar(ranks_tree_frame)
ranks_tree_scroll_y.pack(side='right', fill='y')

ranks_tree_scroll_x = ttk.Scrollbar(ranks_tree_frame, orient='horizontal')
ranks_tree_scroll_x.pack(side='bottom', fill='x')

# Silme butonu için yeni sütun ekle
all_columns = ['Delete'] + ranks_columns
ranks_tree = ttk.Treeview(ranks_tree_frame,
                         columns=all_columns,
                         show='headings',
                         yscrollcommand=ranks_tree_scroll_y.set,
                         xscrollcommand=ranks_tree_scroll_x.set)

ranks_tree_scroll_y.config(command=ranks_tree.yview)
ranks_tree_scroll_x.config(command=ranks_tree.xview)

# Silme butonu sütunu
ranks_tree.heading('Delete', text='')
ranks_tree.column('Delete', width=30, anchor='center')

# Diğer sütunları ayarla
for col in ranks_columns:
    ranks_tree.heading(col, text=col, command=lambda c=col: treeview_sort_column(ranks_tree, c, False))
    ranks_tree.column(col, width=column_widths.get(col, 100), minwidth=50)

ranks_tree.pack(fill='both', expand=True)

def copy_item(event):
    try:
        item = ranks_tree.selection()[0]
        column = ranks_tree.identify_column(event.x)
        col_id = ranks_tree.column(column)['id']
        if col_id != 'Delete':  # Delete sütunu hariç
            value = ranks_tree.item(item)['values'][all_columns.index(col_id)]
            root.clipboard_clear()
            root.clipboard_append(value)
    except Exception as e:
        print(f"Kopyalama hatası: {e}")

def delete_record(event):
    try:
        item = ranks_tree.identify_row(event.y)
        column = ranks_tree.identify_column(event.x)

        if item and column == '#1':  # Silme butonu sütunu
            values = ranks_tree.item(item)['values']
            if values:
                url = values[1]  # URL ikinci sütunda

                # Kullanıcıya sor
                if messagebox.askyesno("Silme Onayı", "Bu kaydı silmek istediğinizden emin misiniz?"):
                    # Google Sheets'ten sil
                    worksheet = ranks_sheet
                    cell = worksheet.find(url)
                    if cell:
                        worksheet.delete_rows(cell.row)

                    # Yerel verilerden sil
                    ranks_data = load_ranks_data()
                    if url in ranks_data:
                        del ranks_data[url]
                        save_ranks_data(ranks_data)

                    # Treeview'dan sil
                    ranks_tree.delete(item)
                    messagebox.showinfo("Bilgi", "Kayıt başarıyla silindi!")
    except Exception as e:
        print(f"Silme hatası: {e}")

def edit_tag(event):
    try:
        item = ranks_tree.selection()[0]
        column = ranks_tree.identify_column(event.x)
        col_id = ranks_tree.column(column)['id']

        # Tag ve Channel sütunlarında düzenlemeye izin ver
        if col_id in ['Tag', 'Channel']:
            x, y, w, h = ranks_tree.bbox(item, column)
            values = ranks_tree.item(item)['values']
            url = values[1]  # URL ikinci sütunda

            def save_tag(event=None):
                try:
                    if not edit_entry.winfo_exists():
                        return

                    new_value = edit_entry.get()
                    ranks_data = load_ranks_data()

                    if url in ranks_data:
                        # Hangi sütunun düzenlendiğini belirle
                        field = col_id
                        ranks_data[url][field] = new_value
                        save_ranks_data(ranks_data)
                        update_ranks_table()

                except Exception as e:
                    print(f"Değer kaydetme hatası: {e}")
                finally:
                    if edit_entry.winfo_exists():
                        edit_entry.destroy()

            edit_entry = ttk.Entry(ranks_tree, style='Custom.TEntry')
            edit_entry.place(x=x, y=y, width=w, height=h)

            # Mevcut değeri göster
            ranks_data = load_ranks_data()
            current_value = ranks_data.get(url, {}).get(col_id, '')
            edit_entry.insert(0, current_value)

            edit_entry.select_range(0, tk.END)
            edit_entry.focus()
            edit_entry.bind('<Return>', save_tag)
            edit_entry.bind('<FocusOut>', save_tag)

    except Exception as e:
        print(f"Düzenleme hatası: {e}")

# Mouse binding'leri
ranks_tree.bind('<Button-1>', lambda e: delete_record(e) if ranks_tree.identify_column(e.x) == '#1' else (
    root.clipboard_clear() or root.clipboard_append(ranks_tree.item(ranks_tree.identify_row(e.y))['values'][1])
    if ranks_tree.identify_column(e.x) == '#2'
    else None
))
ranks_tree.bind('<Double-Button-1>', edit_tag)

# Silme butonu için stil
style = ttk.Style()
style.configure("Treeview", rowheight=25)
style.configure("Treeview.Heading", font=('Calibri', 10, 'bold'))
style.map("Treeview",
          foreground=[('selected', '#000000')],
          background=[('selected', '#f0f0f0')])

# Delete sütunu için özel renk
ranks_tree.tag_configure('delete_button', foreground='red')

# Ranks sekmesindeki tüm URL ve Keyword bilgilerini Toplu Sorgu sekmesine aktarma fonksiyonu
def scan_all_urls_and_keywords():
    try:
        # Ranks tablosundaki tüm verileri al
        all_items = ranks_tree.get_children()
        if not all_items:
            messagebox.showinfo("Bilgi", "Ranks tablosunda veri bulunamadı!")
            return

        # URL ve Keyword bilgilerini topla
        bulk_data = []
        for item in all_items:
            values = ranks_tree.item(item)['values']
            if len(values) >= 3:  # En az 3 sütun olmalı (Delete, URL, Keyword)
                url = values[1]  # URL ikinci sütunda
                keyword = values[2]  # Keyword üçüncü sütunda
                if url and keyword:
                    bulk_data.append(f"{url} | {keyword}")

        if not bulk_data:
            messagebox.showinfo("Bilgi", "İşlenecek URL ve Keyword bulunamadı!")
            return

        # Toplu Sorgu sekmesine geç
        notebook.select(1)  # Toplu Sorgu sekmesi (index 1)

        # Toplu Sorgu metin alanını temizle ve verileri ekle
        bulk_text.delete(1.0, tk.END)
        bulk_text.insert(tk.END, "\n".join(bulk_data))

        # Toplu Tara butonuna tıkla
        bulk_button.invoke()

        messagebox.showinfo("Bilgi", f"{len(bulk_data)} adet URL ve Keyword taranmak üzere aktarıldı!")

    except Exception as e:
        messagebox.showerror("Hata", f"Tarama işlemi sırasında bir hata oluştu: {e}")

# Güncel sekmesi için fonksiyonlar
def scrape_channel_views(channel_url):
    """Kanal toplam izlenme sayısını scrape eder"""
    try:
        print(f"Scraping başlıyor: {channel_url}")

        options = webdriver.ChromeOptions()
        # Artık headless modda çalıştır (daha hızlı)
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)

        driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        # Kanal hakkında sayfasına git
        about_url = channel_url.rstrip('/') + '/about'
        print(f"Gidilen URL: {about_url}")
        driver.get(about_url)
        time.sleep(5)  # Daha uzun bekle

        # Önce "...devamı" butonunu bul ve tıkla
        try:
            print("...devamı butonunu arıyor...")
            # Farklı selector'ları dene
            show_more_selectors = [
                'span[style*="font-weight: 500"]',
                'span[style*="font-weight:500"]',
                'span:contains("devamı")',
                'button:contains("devamı")',
                'tp-yt-paper-button:contains("devamı")',
                '[aria-label*="devamı"]'
            ]

            show_more_clicked = False
            for selector in show_more_selectors:
                try:
                    if 'contains' in selector:
                        # JavaScript ile contains kullan
                        elements = driver.execute_script(f"""
                            return Array.from(document.querySelectorAll('span, button')).filter(el =>
                                el.textContent.includes('devamı') || el.textContent.includes('Show more')
                            );
                        """)
                    else:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)

                    for element in elements:
                        try:
                            text = element.text.strip().lower()
                            if 'devamı' in text or 'show more' in text or 'more' in text:
                                print(f"Devamı butonu bulundu: {element.text}")
                                driver.execute_script("arguments[0].click();", element)
                                time.sleep(2)
                                show_more_clicked = True
                                break
                        except:
                            continue
                    if show_more_clicked:
                        break
                except Exception as e:
                    print(f"Selector hatası {selector}: {e}")
                    continue

            if not show_more_clicked:
                print("Devamı butonu bulunamadı, devam ediliyor...")

        except Exception as e:
            print(f"Devamı butonu tıklama hatası: {e}")

        # Toplam görüntüleme sayısını bul
        try:
            print("İzlenme sayısını arıyor...")

            # Farklı selector'ları sırayla dene
            selectors = [
                'td.style-scope.ytd-about-channel-renderer',
                'td[class*="style-scope"][class*="ytd-about-channel-renderer"]',
                'td.ytd-about-channel-renderer',
                'td[class*="about-channel-renderer"]',
                'td',
                'span[class*="style-scope"]',
                'yt-formatted-string'
            ]

            views_found = False
            for selector in selectors:
                try:
                    # print(f"Denenen selector: {selector}")
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    # print(f"Bulunan element sayısı: {len(elements)}")

                    for i, element in enumerate(elements):
                        try:
                            text = element.text.strip()
                            # print(f"Element {i}: '{text}'")

                            # Türkçe ve İngilizce kontrol
                            if ('görüntüleme' in text.lower() or 'views' in text.lower()) and any(char.isdigit() for char in text):
                                print(f"İzlenme sayısı bulundu: {text}")

                                import re
                                # Sayıları çıkar (nokta ve virgül dahil)
                                numbers = re.findall(r'[\d.,]+', text)
                                if numbers:
                                    views_number = numbers[0].replace('.', '').replace(',', '').strip()
                                    print(f"Çıkarılan sayı: {views_number}")

                                    if views_number.isdigit():
                                        result = int(views_number)
                                        print(f"Başarılı sonuç: {result:,}")
                                        driver.quit()
                                        return result
                        except Exception as elem_error:
                            print(f"Element okuma hatası: {elem_error}")
                            continue

                    if views_found:
                        break

                except Exception as selector_error:
                    print(f"Selector hatası {selector}: {selector_error}")
                    continue

            # Eğer hiçbir şey bulunamazsa, sayfanın HTML'ini kontrol et
            print("Hiçbir izlenme sayısı bulunamadı. Sayfa HTML'ini kontrol ediliyor...")

            # Screenshot al (sadece hata durumunda)
            # try:
            #     driver.save_screenshot("debug_screenshot.png")
            #     print("Screenshot kaydedildi: debug_screenshot.png")
            # except:
            #     pass

            # Sayfadaki tüm text içeriğini kontrol et
            try:
                all_text = driver.find_element(By.TAG_NAME, "body").text
                # print("Sayfa içeriği (ilk 1000 karakter):")
                # print(all_text[:1000])
                # print("...")

                # Text içinde izlenme sayısını ara
                import re
                view_patterns = [
                    r'(\d+(?:[.,]\d+)*)\s*görüntüleme',
                    r'(\d+(?:[.,]\d+)*)\s*views',
                    r'(\d+(?:[.,]\d+)*)\s*view'
                ]

                for pattern in view_patterns:
                    matches = re.findall(pattern, all_text, re.IGNORECASE)
                    if matches:
                        print(f"Text'te bulunan pattern: {pattern}")
                        print(f"Bulunan değerler: {matches}")

                        for match in matches:
                            views_number = match.replace('.', '').replace(',', '').strip()
                            if views_number.isdigit() and len(views_number) > 3:  # En az 4 haneli olsun
                                result = int(views_number)
                                print(f"Text'ten çıkarılan sonuç: {result:,}")
                                driver.quit()
                                return result
            except Exception as text_error:
                print(f"Text okuma hatası: {text_error}")

            # HTML source'u kontrol et
            page_source = driver.page_source

            # HTML içinde "görüntüleme" veya "views" kelimesini ara
            import re
            view_patterns = [
                r'(\d+(?:[.,]\d+)*)\s*görüntüleme',
                r'(\d+(?:[.,]\d+)*)\s*views',
                r'>(\d+(?:[.,]\d+)*)\s*görüntüleme<',
                r'>(\d+(?:[.,]\d+)*)\s*views<',
                r'(\d{1,3}(?:[.,]\d{3})*)\s*görüntüleme',
                r'(\d{1,3}(?:[.,]\d{3})*)\s*views'
            ]

            for pattern in view_patterns:
                matches = re.findall(pattern, page_source, re.IGNORECASE)
                if matches:
                    print(f"HTML'de bulunan pattern: {pattern}")
                    print(f"Bulunan değerler: {matches}")

                    for match in matches:
                        views_number = match.replace('.', '').replace(',', '').strip()
                        if views_number.isdigit() and len(views_number) > 3:
                            result = int(views_number)
                            print(f"HTML'den çıkarılan sonuç: {result:,}")
                            driver.quit()
                            return result

        except Exception as e:
            print(f"Genel element bulma hatası: {e}")

        driver.quit()
        return 0

    except Exception as e:
        print(f"Scraping hatası: {e}")
        return 0

def load_daily_views_data():
    """Günlük izlenme verilerini yükler"""
    try:
        if os.path.exists('daily_views.json'):
            with open('daily_views.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    except Exception as e:
        print(f"Günlük veri yükleme hatası: {e}")
        return {}

def save_daily_views_data(data):
    """Günlük izlenme verilerini kaydeder"""
    try:
        with open('daily_views.json', 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
    except Exception as e:
        print(f"Günlük veri kaydetme hatası: {e}")

def load_channels_data2():
    """channels_data2.json dosyasından kanal verilerini yükler"""
    try:
        if os.path.exists('channels_data2.json'):
            with open('channels_data2.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    except Exception as e:
        print(f"channels_data2.json yükleme hatası: {e}")
        return {}

def update_channel_views():
    """Tüm kanalların izlenme sayılarını günceller"""
    try:
        # channels_data2.json dosyasından kanal verilerini yükle
        channels_data = load_channels_data2()
        daily_data = load_daily_views_data()
        today = datetime.now().strftime("%Y-%m-%d")

        if today not in daily_data:
            daily_data[today] = {}

        channel_count = len(channels_data)
        if channel_count == 0:
            messagebox.showinfo("Bilgi", "channels_data2.json dosyasında kanal bulunamadı!")
            return

        # Progress bar'ı başlat
        guncel_progress_var.set(0)
        info_label.config(text="Kanallar güncelleniyor...")
        root.update()

        total_views_today = 0
        successful_channels = 0
        failed_channels = 0

        # Tüm kanalları işle
        for i, (channel_url, channel_info) in enumerate(channels_data.items()):
            channel_name = channel_info.get('name', '')

            if channel_name:
                # Progress bar'ı güncelle
                progress = (i / channel_count) * 100
                guncel_progress_var.set(progress)
                info_label.config(text=f"Güncelleniyor: {channel_name} ({i+1}/{channel_count})")
                root.update()

                print(f"İşlenen kanal: {channel_name} - {channel_url}")

                # Kanal izlenme sayısını scrape et
                views = scrape_channel_views(channel_url)

                if views > 0:
                    daily_data[today][channel_name] = views
                    total_views_today += views
                    successful_channels += 1
                    print(f"Başarılı! {channel_name}: {views:,} izlenme")
                else:
                    failed_channels += 1
                    print(f"Başarısız! {channel_name}: İzlenme sayısı alınamadı")

                # Kısa bir bekleme (YouTube'un rate limiting'ini önlemek için)
                time.sleep(2)

        # Toplam izlenmeyi kaydet
        daily_data[today]['TOPLAM'] = total_views_today
        save_daily_views_data(daily_data)

        # Progress bar'ı tamamla
        guncel_progress_var.set(100)
        info_label.config(text="Güncelleme tamamlandı!")
        root.update()

        # Tabloyu güncelle
        update_guncel_table()

        # Progress bar'ı sıfırla
        root.after(3000, lambda: (guncel_progress_var.set(0), info_label.config(text="Son 7 günün kanal izlenme verileri")))

        # Sonuç mesajı
        result_message = f"""Güncelleme Tamamlandı!

Toplam Kanal: {channel_count}
Başarılı: {successful_channels}
Başarısız: {failed_channels}
Toplam İzlenme: {total_views_today:,}"""

        messagebox.showinfo("Güncelleme Tamamlandı", result_message)

    except Exception as e:
        guncel_progress_var.set(0)
        info_label.config(text="Hata oluştu!")
        messagebox.showerror("Hata", f"Güncelleme sırasında hata: {e}")
        print(f"Detaylı hata: {e}")

def select_month(month):
    """Ay seçimi fonksiyonu"""
    selected_month.set(month)
    # Seçili ay butonunu vurgula
    for i, btn in enumerate(month_buttons):
        if i + 1 == month:
            btn.configure(style='Selected.TButton')
        else:
            btn.configure(style='TButton')
    update_guncel_table()

def get_month_dates(year, month):
    """Belirtilen ay için tüm günleri döndürür"""
    import calendar
    days_in_month = calendar.monthrange(year, month)[1]
    dates = []
    for day in range(1, days_in_month + 1):
        date_str = f"{year:04d}-{month:02d}-{day:02d}"
        dates.append(date_str)
    return dates

def update_guncel_table():
    """Güncel tablosunu seçili ay/yıl için günceller"""
    try:
        # Tabloyu temizle
        for item in guncel_tree.get_children():
            guncel_tree.delete(item)

        daily_data = load_daily_views_data()
        channels_data = load_channels_data2()  # channels_data2.json kullan

        # Seçili ay ve yılın tüm günlerini al
        year = selected_year.get()
        month = selected_month.get()
        month_dates = get_month_dates(year, month)

        # Sütun başlıklarını güncelle (günleri göster)
        new_columns = ['Kanal']
        for date in month_dates:
            # Günü göster (sadece gün numarası)
            try:
                date_obj = datetime.strptime(date, "%Y-%m-%d")
                day = date_obj.strftime("%d")
                new_columns.append(day)
            except:
                new_columns.append(date[-2:])  # Son 2 karakter (gün)
        new_columns.append('Toplam')
        new_columns.append('Değişim')

        # Treeview sütunlarını yeniden oluştur
        guncel_tree['columns'] = new_columns
        guncel_tree['show'] = 'headings'

        # Sütun başlıklarını ayarla
        for col in new_columns:
            guncel_tree.heading(col, text=col)
            if col == 'Kanal':
                guncel_tree.column(col, width=120, minwidth=100)
            elif col in ['Toplam', 'Değişim']:
                guncel_tree.column(col, width=100, minwidth=80)
            else:
                guncel_tree.column(col, width=50, minwidth=40)

        # Her kanal için satır ekle
        for channel_url, channel_info in channels_data.items():
            channel_name = channel_info.get('name', '')
            if channel_name:
                row_data = [channel_name]
                monthly_total = 0

                # Ayın her günü için veri ekle
                for date in month_dates:
                    views = daily_data.get(date, {}).get(channel_name, 0)
                    if views > 0:
                        row_data.append(f"{views:,}")
                        monthly_total += views
                    else:
                        row_data.append("-")

                # Aylık toplam
                row_data.append(f"{monthly_total:,}" if monthly_total > 0 else "-")

                # Değişim hesapla (ayın ilk ve son günü)
                first_day_views = daily_data.get(month_dates[0], {}).get(channel_name, 0)
                last_day_views = daily_data.get(month_dates[-1], {}).get(channel_name, 0)
                if first_day_views > 0 and last_day_views > 0:
                    change = last_day_views - first_day_views
                    change_text = f"+{change:,}" if change > 0 else f"{change:,}"
                    row_data.append(change_text)
                else:
                    row_data.append("-")

                guncel_tree.insert('', 'end', values=row_data)

        # Toplam satırı ekle
        total_row = ['TOPLAM']
        grand_total = 0

        for date in month_dates:
            total_views = daily_data.get(date, {}).get('TOPLAM', 0)
            if total_views > 0:
                total_row.append(f"{total_views:,}")
                grand_total += total_views
            else:
                total_row.append("-")

        # Genel toplam
        total_row.append(f"{grand_total:,}" if grand_total > 0 else "-")

        # Toplam değişim
        first_day_total = daily_data.get(month_dates[0], {}).get('TOPLAM', 0)
        last_day_total = daily_data.get(month_dates[-1], {}).get('TOPLAM', 0)
        if first_day_total > 0 and last_day_total > 0:
            total_change = last_day_total - first_day_total
            change_text = f"+{total_change:,}" if total_change > 0 else f"{total_change:,}"
            total_row.append(change_text)
        else:
            total_row.append("-")

        guncel_tree.insert('', 'end', values=total_row, tags=('total',))

        # Toplam satırını vurgula
        guncel_tree.tag_configure('total', background='#e6f3ff', font=('Segoe UI', 10, 'bold'))

        # Seçili ay butonunu vurgula
        for i, btn in enumerate(month_buttons):
            if i + 1 == month:
                btn.configure(style='Selected.TButton')
            else:
                btn.configure(style='TButton')

    except Exception as e:
        print(f"Tablo güncelleme hatası: {e}")

# Güncel sekmesi UI bileşenlerini oluştur
# Güncel sekmesi için üst frame (butonlar)
guncel_top_frame = ttk.Frame(guncel_frame, style='Custom.TFrame')
guncel_top_frame.pack(fill=tk.X, padx=10, pady=10)

# Sol taraf - Ana butonlar
left_buttons_frame = ttk.Frame(guncel_top_frame, style='Custom.TFrame')
left_buttons_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

# Güncelle butonu
update_views_button = ttk.Button(
    left_buttons_frame,
    text="İzlenme Sayılarını Güncelle",
    style='Custom.TButton',
    command=update_channel_views
)
update_views_button.pack(side=tk.LEFT, padx=5)

# Yenile butonu
refresh_table_button = ttk.Button(
    left_buttons_frame,
    text="Tabloyu Yenile",
    style='Custom.TButton',
    command=update_guncel_table
)
refresh_table_button.pack(side=tk.LEFT, padx=5)

# Orta kısım - Ay ve Yıl seçimi
date_selection_frame = ttk.Frame(guncel_top_frame, style='Custom.TFrame')
date_selection_frame.pack(side=tk.LEFT, padx=20)

# Ay butonları frame
month_frame = ttk.Frame(date_selection_frame, style='Custom.TFrame')
month_frame.pack(side=tk.TOP, pady=2)

# Seçili ay ve yıl için değişkenler
selected_month = tk.IntVar(value=datetime.now().month)
selected_year = tk.IntVar(value=datetime.now().year)

# Ay butonları (1-12)
month_buttons = []
for month in range(1, 13):
    btn = ttk.Button(
        month_frame,
        text=str(month),
        width=3,
        command=lambda m=month: select_month(m)
    )
    btn.pack(side=tk.LEFT, padx=1)
    month_buttons.append(btn)

# Yıl seçimi frame
year_frame = ttk.Frame(date_selection_frame, style='Custom.TFrame')
year_frame.pack(side=tk.TOP, pady=2)

# Yıl seçimi combobox
year_label = ttk.Label(year_frame, text="Yıl:", style='Custom.TLabel')
year_label.pack(side=tk.LEFT, padx=2)

year_combobox = ttk.Combobox(
    year_frame,
    textvariable=selected_year,
    values=[2025, 2026, 2027, 2028, 2029, 2030],
    width=8,
    state="readonly"
)
year_combobox.pack(side=tk.LEFT, padx=2)
year_combobox.bind('<<ComboboxSelected>>', lambda e: update_guncel_table())

# Progress bar için frame
progress_frame = ttk.Frame(guncel_top_frame, style='Custom.TFrame')
progress_frame.pack(side=tk.RIGHT, padx=10)

# Progress bar
guncel_progress_var = tk.DoubleVar()
guncel_progress_bar = ttk.Progressbar(
    progress_frame,
    variable=guncel_progress_var,
    maximum=100,
    mode='determinate',
    length=200
)
guncel_progress_bar.pack(side=tk.TOP, pady=2)

# Bilgi etiketi
info_label = ttk.Label(
    progress_frame,
    text="Son 7 günün kanal izlenme verileri",
    style='Custom.TLabel',
    font=('Segoe UI', 9, 'italic')
)
info_label.pack(side=tk.TOP)

# Güncel tablosu için frame
guncel_table_frame = ttk.Frame(guncel_frame, style='Custom.TFrame')
guncel_table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

# Güncel tablosu için scrollbar'lar
guncel_scroll_y = ttk.Scrollbar(guncel_table_frame)
guncel_scroll_y.pack(side='right', fill='y')

guncel_scroll_x = ttk.Scrollbar(guncel_table_frame, orient='horizontal')
guncel_scroll_x.pack(side='bottom', fill='x')

# Güncel tablosu sütunları (dinamik olarak ayın günleri)
guncel_columns = ['Kanal']  # Başlangıçta sadece kanal sütunu

# Güncel tablosu
guncel_tree = ttk.Treeview(
    guncel_table_frame,
    columns=guncel_columns,
    show='headings',
    yscrollcommand=guncel_scroll_y.set,
    xscrollcommand=guncel_scroll_x.set
)

guncel_scroll_y.config(command=guncel_tree.yview)
guncel_scroll_x.config(command=guncel_tree.xview)

guncel_tree.pack(fill='both', expand=True)

# Seçili buton stili oluştur
style = ttk.Style()
style.configure('Selected.TButton',
                background='#0078d4',
                foreground='white',
                font=('Segoe UI', 9, 'bold'))

# İlk yükleme için mevcut ayı seç
current_month = datetime.now().month
select_month(current_month)

# Ayarlar sekmesi UI bileşenlerini oluştur
# Ayarlar sekmesi için ana frame
ayarlar_main_frame = ttk.Frame(ayarlar_frame, style='Custom.TFrame')
ayarlar_main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

# Başlık
ayarlar_title = ttk.Label(
    ayarlar_main_frame,
    text="Uygulama Ayarları",
    style='Custom.TLabel',
    font=('Segoe UI', 16, 'bold')
)
ayarlar_title.pack(pady=(0, 20))

# Otomatik güncelleme ayarları frame
auto_update_frame = ttk.LabelFrame(ayarlar_main_frame, text="Otomatik İzlenme Güncelleme", style='Custom.TFrame')
auto_update_frame.pack(fill=tk.X, pady=10)

# Otomatik güncelleme aktif/pasif
auto_update_var = tk.BooleanVar(value=False)
auto_update_checkbox = ttk.Checkbutton(
    auto_update_frame,
    text="Otomatik güncellemeyi etkinleştir",
    variable=auto_update_var,
    style='Custom.TCheckbutton',
    command=lambda: toggle_auto_update()
)
auto_update_checkbox.pack(anchor='w', padx=10, pady=10)

# Saat ayarı frame
time_frame = ttk.Frame(auto_update_frame, style='Custom.TFrame')
time_frame.pack(fill=tk.X, padx=10, pady=5)

# Saat seçimi
time_label = ttk.Label(time_frame, text="Güncelleme saati:", style='Custom.TLabel')
time_label.pack(side=tk.LEFT, padx=5)

hour_var = tk.StringVar(value="00")
minute_var = tk.StringVar(value="00")

hour_spinbox = ttk.Spinbox(
    time_frame,
    from_=0,
    to=23,
    width=5,
    textvariable=hour_var,
    format="%02.0f"
)
hour_spinbox.pack(side=tk.LEFT, padx=2)

ttk.Label(time_frame, text=":", style='Custom.TLabel').pack(side=tk.LEFT)

minute_spinbox = ttk.Spinbox(
    time_frame,
    from_=0,
    to=59,
    width=5,
    textvariable=minute_var,
    format="%02.0f"
)
minute_spinbox.pack(side=tk.LEFT, padx=2)

ttk.Label(time_frame, text="(24 saat formatı)", style='Custom.TLabel').pack(side=tk.LEFT, padx=10)

# Durum bilgisi
status_frame = ttk.Frame(auto_update_frame, style='Custom.TFrame')
status_frame.pack(fill=tk.X, padx=10, pady=10)

status_label = ttk.Label(
    status_frame,
    text="Durum: Otomatik güncelleme kapalı",
    style='Custom.TLabel',
    font=('Segoe UI', 10, 'italic')
)
status_label.pack(anchor='w')

next_update_label = ttk.Label(
    status_frame,
    text="Sonraki güncelleme: -",
    style='Custom.TLabel',
    font=('Segoe UI', 10, 'italic')
)
next_update_label.pack(anchor='w')

# Test butonu
test_button = ttk.Button(
    auto_update_frame,
    text="Şimdi Test Et",
    style='Custom.TButton',
    command=lambda: test_auto_update()
)
test_button.pack(pady=10)

# Ayarları kaydet butonu
save_settings_button = ttk.Button(
    auto_update_frame,
    text="Ayarları Kaydet",
    style='Custom.TButton',
    command=lambda: save_auto_update_settings()
)
save_settings_button.pack(pady=5)

# Otomatik güncelleme sistemi fonksiyonları
def load_auto_update_settings():
    """Otomatik güncelleme ayarlarını yükler"""
    try:
        if os.path.exists('auto_update_settings.json'):
            with open('auto_update_settings.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        return {
            'enabled': False,
            'hour': 0,
            'minute': 0,
            'last_update': None
        }
    except Exception as e:
        print(f"Ayar yükleme hatası: {e}")
        return {'enabled': False, 'hour': 0, 'minute': 0, 'last_update': None}

def save_auto_update_settings():
    """Otomatik güncelleme ayarlarını kaydeder"""
    try:
        settings = {
            'enabled': auto_update_var.get(),
            'hour': int(hour_var.get()),
            'minute': int(minute_var.get()),
            'last_update': None
        }

        with open('auto_update_settings.json', 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=4)

        messagebox.showinfo("Başarılı", "Ayarlar kaydedildi!")
        update_status_labels()

    except Exception as e:
        messagebox.showerror("Hata", f"Ayar kaydetme hatası: {e}")

def toggle_auto_update():
    """Otomatik güncellemeyi açar/kapatır"""
    if auto_update_var.get():
        # Açıldığında timer'ı başlat
        start_auto_update_timer()
    else:
        # Kapatıldığında timer'ı durdur
        stop_auto_update_timer()

    update_status_labels()

def update_status_labels():
    """Durum etiketlerini günceller"""
    if auto_update_var.get():
        hour = hour_var.get()
        minute = minute_var.get()
        status_label.config(text=f"Durum: Otomatik güncelleme aktif ({hour}:{minute})")

        # Sonraki güncelleme zamanını hesapla
        now = datetime.now()
        next_update = now.replace(hour=int(hour), minute=int(minute), second=0, microsecond=0)

        # Eğer bugünkü saat geçtiyse, yarına ayarla
        if next_update <= now:
            next_update += timedelta(days=1)

        next_update_label.config(text=f"Sonraki güncelleme: {next_update.strftime('%d.%m.%Y %H:%M')}")
    else:
        status_label.config(text="Durum: Otomatik güncelleme kapalı")
        next_update_label.config(text="Sonraki güncelleme: -")

def test_auto_update():
    """Test amaçlı güncelleme yapar"""
    messagebox.showinfo("Test", "Test güncelleme başlatılıyor...")
    # Güncel sekmesine geç
    notebook.select(1)  # Güncel sekmesi
    # Güncelleme fonksiyonunu çağır
    update_channel_views()

# Timer değişkenleri
auto_update_timer = None
auto_update_job = None

def start_auto_update_timer():
    """Otomatik güncelleme timer'ını başlatır"""
    global auto_update_timer, auto_update_job

    def check_time():
        global auto_update_job
        try:
            now = datetime.now()
            target_hour = int(hour_var.get())
            target_minute = int(minute_var.get())

            # Hedef zamanı kontrol et
            if now.hour == target_hour and now.minute == target_minute and now.second < 30:
                print(f"Otomatik güncelleme başlatılıyor: {now.strftime('%H:%M:%S')}")

                # Güncel sekmesine geç
                notebook.select(1)  # Güncel sekmesi

                # Güncelleme fonksiyonunu çağır
                root.after(1000, update_channel_views)  # 1 saniye sonra başlat

                # Son güncelleme zamanını kaydet
                settings = load_auto_update_settings()
                settings['last_update'] = now.strftime('%Y-%m-%d %H:%M:%S')
                with open('auto_update_settings.json', 'w', encoding='utf-8') as f:
                    json.dump(settings, f, ensure_ascii=False, indent=4)

            # Eğer otomatik güncelleme hala aktifse, 30 saniye sonra tekrar kontrol et
            if auto_update_var.get():
                auto_update_job = root.after(30000, check_time)  # 30 saniye

        except Exception as e:
            print(f"Timer hatası: {e}")

    # İlk kontrolü başlat
    auto_update_job = root.after(1000, check_time)  # 1 saniye sonra başlat

def stop_auto_update_timer():
    """Otomatik güncelleme timer'ını durdurur"""
    global auto_update_job
    if auto_update_job:
        root.after_cancel(auto_update_job)
        auto_update_job = None

# Uygulama başladığında ve sekme değiştiğinde Ranks tablosunu yükle
def on_notebook_tab_changed(event):
    try:
        selected_tab = event.widget.select()
        tab_text = event.widget.tab(selected_tab, "text")
        if tab_text == "Ranks":
            update_ranks_table()
        elif tab_text == "Güncel":
            update_guncel_table()
        elif tab_text == "Ayarlar":
            # Ayarlar sekmesine geçildiğinde ayarları yükle
            load_and_apply_settings()
    except Exception as e:
        print(f"Sekme değişim hatası: {e}")

def load_and_apply_settings():
    """Kayıtlı ayarları yükler ve uygular"""
    try:
        settings = load_auto_update_settings()
        auto_update_var.set(settings.get('enabled', False))
        hour_var.set(f"{settings.get('hour', 0):02d}")
        minute_var.set(f"{settings.get('minute', 0):02d}")

        if settings.get('enabled', False):
            start_auto_update_timer()

        update_status_labels()

    except Exception as e:
        print(f"Ayar yükleme hatası: {e}")

# Sekme değiştiğinde tabloyu güncelle
notebook.bind('<<NotebookTabChanged>>', on_notebook_tab_changed)

# Sıralama ve filtreleme değişikliklerinde tabloyu güncelle
sort_var.trace_add('write', lambda *args: update_ranks_table())
tag_filter.bind('<KeyRelease>', lambda *args: update_ranks_table())

# İlk yükleme
update_ranks_table()

# Otomatik güncelleme ayarlarını yükle ve başlat
try:
    settings = load_auto_update_settings()
    auto_update_var.set(settings.get('enabled', False))
    hour_var.set(f"{settings.get('hour', 0):02d}")
    minute_var.set(f"{settings.get('minute', 0):02d}")

    if settings.get('enabled', False):
        start_auto_update_timer()
        print(f"Otomatik güncelleme aktif: {hour_var.get()}:{minute_var.get()}")

    update_status_labels()
except Exception as e:
    print(f"Otomatik güncelleme ayarları yükleme hatası: {e}")

# Uygulama penceresini başlat
root.mainloop()